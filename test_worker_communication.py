#!/usr/bin/env python3
"""
Test Worker Communication
Simple test to verify worker thread message parsing is working
"""

import sys
import time
import json

def test_worker_communication():
    """Test worker communication with structured messages"""
    
    print("Starting worker communication test...")
    
    # Test progress updates
    for i in range(0, 101, 20):
        progress_data = {
            "type": "progress",
            "percentage": i,
            "message": f"Processing step {i//20 + 1}",
            "phase": "TEST_PHASE",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        print(f"PROGRESS_UPDATE:{json.dumps(progress_data)}")
        sys.stdout.flush()
        time.sleep(0.5)
    
    # Test real-time updates
    for i in range(3):
        realtime_data = {
            "type": "test_update",
            "message": f"Real-time update {i+1}",
            "data": {"step": i+1, "total": 3},
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        print(f"REALTIME_UPDATE:{json.dumps(realtime_data)}")
        sys.stdout.flush()
        time.sleep(0.5)
    
    # Test completion
    print("SUCCESS:test_worker_123")
    
    return True

if __name__ == "__main__":
    test_worker_communication()
