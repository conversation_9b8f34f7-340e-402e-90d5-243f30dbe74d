<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pre-Reporting UI Fix Test</title>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        #pre-reporting-container {
            min-height: 400px;
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.primary {
            background: #007bff;
        }
        
        .pre-reporting-interface {
            background: white;
            border-radius: 10px;
            padding: 20px;
        }
        
        .pre-reporting-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .summary-stats {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 Pre-Reporting UI Fix Test</h1>
            <p>Testing the fixed InteractivePreReporting class</p>
        </div>
        
        <div class="test-section">
            <h3>📋 Test Results</h3>
            <div id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h3>🎨 UI Container</h3>
            <button onclick="testUILoad()">🚀 Test UI Loading</button>
            <button onclick="clearContainer()">🧹 Clear Container</button>
            <div id="pre-reporting-container">
                <p style="text-align: center; color: #666;">Click "Test UI Loading" to test the fixed UI</p>
            </div>
        </div>
    </div>

    <script>
        // Mock API for testing
        window.api = {
            getLatestPreReportingData: async () => {
                // Simulate the real API response with sample data
                return {
                    success: true,
                    data: [
                        {
                            id: 1,
                            employee_id: 'EMP001',
                            employee_name: 'John Doe',
                            section_name: 'Personal Details',
                            item_label: 'Basic Salary',
                            previous_value: '5000.00',
                            current_value: '5500.00',
                            change_type: 'INCREASED',
                            priority: 'HIGH',
                            selected_for_report: true
                        },
                        {
                            id: 2,
                            employee_id: 'EMP002',
                            employee_name: 'Jane Smith',
                            section_name: 'Deductions',
                            item_label: 'Tax',
                            previous_value: '800.00',
                            current_value: '850.00',
                            change_type: 'INCREASED',
                            priority: 'MODERATE',
                            selected_for_report: false
                        }
                    ],
                    session_id: 'test_session_123',
                    total_changes: 2
                };
            }
        };

        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(resultDiv);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        async function testUILoad() {
            logResult('🚀 Starting UI load test...', 'info');
            
            try {
                // Clear previous results
                document.getElementById('test-results').innerHTML = '';
                
                // Step 1: Load the InteractivePreReporting script
                logResult('📋 Loading InteractivePreReporting script...', 'info');
                
                if (!window.InteractivePreReporting) {
                    await loadScript('./ui/interactive_pre_reporting.js');
                    logResult('✅ InteractivePreReporting script loaded', 'success');
                } else {
                    logResult('✅ InteractivePreReporting already available', 'success');
                }
                
                // Step 2: Get container
                const container = document.getElementById('pre-reporting-container');
                if (!container) {
                    throw new Error('Container not found');
                }
                logResult('✅ Container found', 'success');
                
                // Step 3: Create InteractivePreReporting instance
                logResult('🎨 Creating InteractivePreReporting instance...', 'info');
                const preReporting = new window.InteractivePreReporting(container);
                logResult('✅ InteractivePreReporting instance created', 'success');
                
                // Step 4: Initialize the UI
                logResult('🔄 Initializing UI...', 'info');
                preReporting.initialize();
                logResult('✅ UI initialization started', 'success');
                
                // Wait a bit for async operations
                setTimeout(() => {
                    logResult('🎉 UI test completed successfully!', 'success');
                }, 2000);
                
            } catch (error) {
                logResult(`❌ UI test failed: ${error.message}`, 'error');
                console.error('UI test error:', error);
            }
        }

        function clearContainer() {
            const container = document.getElementById('pre-reporting-container');
            container.innerHTML = '<p style="text-align: center; color: #666;">Container cleared</p>';
            logResult('🧹 Container cleared', 'info');
        }

        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        // Initialize
        logResult('🧪 Test page loaded and ready', 'info');
    </script>
</body>
</html>
