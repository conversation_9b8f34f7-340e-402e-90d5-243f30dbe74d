#!/usr/bin/env python3
"""
Fix Unicode Issues in Scripts
Replace emoji characters with ASCII equivalents for Windows compatibility
"""

import os
import re

def fix_unicode_in_file(filepath):
    """Fix unicode issues in a single file"""
    
    # Emoji to ASCII mapping
    emoji_replacements = {
        '🔧': '[FIX]',
        '🧪': '[TEST]',
        '🔍': '[CHECK]',
        '📊': '[DATA]',
        '📅': '[DATE]',
        '🏢': '[DEPT]',
        '🔢': '[NUM]',
        '📋': '[LIST]',
        '👤': '[USER]',
        '🏷️': '[TAG]',
        '📄': '[DOC]',
        '✅': '[OK]',
        '❌': '[FAIL]',
        '⚠️': '[WARN]',
        'ℹ️': '[INFO]',
        '🎉': '[SUCCESS]',
        '📈': '[REPORT]',
        '🕒': '[TIME]',
        '🚀': '[START]',
        '💾': '[SAVE]',
        '🔄': '[PROCESS]',
        '📝': '[WRITE]',
        '🎯': '[TARGET]',
        '🔒': '[SECURE]',
        '🌟': '[STAR]',
        '⭐': '[STAR]',
        '🎊': '[PARTY]',
        '🎈': '[BALLOON]',
        '🎁': '[GIFT]',
        '🏆': '[TROPHY]',
        '🥇': '[GOLD]',
        '🥈': '[SILVER]',
        '🥉': '[BRONZE]',
        '💡': '[IDEA]',
        '🔥': '[HOT]',
        '⚡': '[FAST]',
        '💪': '[STRONG]',
        '👍': '[GOOD]',
        '👎': '[BAD]',
        '🤝': '[SHAKE]',
        '🙏': '[THANKS]',
        '💯': '[100]',
        '🎪': '[CIRCUS]',
        '🎭': '[MASK]',
        '🎨': '[ART]',
        '🎵': '[MUSIC]',
        '🎶': '[NOTE]',
        '🎸': '[GUITAR]',
        '🎹': '[PIANO]',
        '🎺': '[TRUMPET]',
        '🎻': '[VIOLIN]',
        '🥁': '[DRUM]',
        '🎤': '[MIC]',
        '🎧': '[HEADPHONE]',
        '📻': '[RADIO]',
        '📺': '[TV]',
        '📱': '[PHONE]',
        '💻': '[LAPTOP]',
        '🖥️': '[DESKTOP]',
        '⌨️': '[KEYBOARD]',
        '🖱️': '[MOUSE]',
        '🖨️': '[PRINTER]',
        '💿': '[CD]',
        '💾': '[FLOPPY]',
        '💽': '[DISK]',
        '📀': '[DVD]',
        '🎮': '[GAME]',
        '🕹️': '[JOYSTICK]',
        '🎲': '[DICE]',
        '♠️': '[SPADE]',
        '♥️': '[HEART]',
        '♦️': '[DIAMOND]',
        '♣️': '[CLUB]',
        '🃏': '[JOKER]',
        '🀄': '[MAHJONG]',
        '🎯': '[DART]',
        '🏀': '[BASKETBALL]',
        '🏈': '[FOOTBALL]',
        '⚽': '[SOCCER]',
        '🎾': '[TENNIS]',
        '🏐': '[VOLLEYBALL]',
        '🏓': '[PINGPONG]',
        '🏸': '[BADMINTON]',
        '🥅': '[GOAL]',
        '🏒': '[HOCKEY]',
        '🏑': '[FIELD_HOCKEY]',
        '🥍': '[LACROSSE]',
        '🏏': '[CRICKET]',
        '⛳': '[GOLF]',
        '🏹': '[ARCHERY]',
        '🎣': '[FISHING]',
        '🥊': '[BOXING]',
        '🥋': '[MARTIAL_ARTS]',
        '🎪': '[TENT]',
        '🎨': '[PALETTE]',
        '🎭': '[THEATER]',
        '🎪': '[CIRCUS]',
        '🎨': '[ART]',
        '🎭': '[DRAMA]',
        '🎪': '[SHOW]',
        '🎨': '[PAINT]',
        '🎭': '[COMEDY]',
        '🎪': '[PERFORMANCE]'
    }
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace emojis
        for emoji, replacement in emoji_replacements.items():
            content = content.replace(emoji, replacement)
        
        # Write back
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"[OK] Fixed unicode issues in {filepath}")
        return True
        
    except Exception as e:
        print(f"[FAIL] Error fixing {filepath}: {e}")
        return False

def main():
    """Fix unicode issues in all Python scripts"""
    
    print("[FIX] FIXING UNICODE ISSUES IN SCRIPTS")
    print("=" * 50)
    
    # Files to fix
    files_to_fix = [
        'fix_section_misclassification.py',
        'fix_ghana_card_misclassification.py',
        'test_pre_reporting_fixes.py'
    ]
    
    success_count = 0
    
    for filepath in files_to_fix:
        if os.path.exists(filepath):
            if fix_unicode_in_file(filepath):
                success_count += 1
        else:
            print(f"[WARN] File not found: {filepath}")
    
    print(f"\n[RESULT] Fixed {success_count}/{len(files_to_fix)} files")
    
    if success_count == len(files_to_fix):
        print("[SUCCESS] All unicode issues fixed!")
        return True
    else:
        print("[WARN] Some files could not be fixed")
        return False

if __name__ == "__main__":
    main()
