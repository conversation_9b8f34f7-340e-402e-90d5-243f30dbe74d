/**
 * PYTHON WORKER MODULE
 * Worker thread implementation for non-blocking Python script execution
 * Provides high-performance processing for large payroll files
 */

const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const { spawn } = require('child_process');
const path = require('path');

/**
 * Create a Python worker for non-blocking script execution
 * @param {string} scriptPath - Path to Python script
 * @param {Array} args - Arguments for the script
 * @param {Object} options - Worker options
 * @returns {Promise} - Promise that resolves with script output
 */
function createPythonWorker(scriptPath, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    // Create worker thread
    const worker = new Worker(__filename, {
      workerData: {
        scriptPath,
        args,
        options
      }
    });

    // Handle worker messages
    worker.on('message', (result) => {
      if (result.success) {
        resolve(result.output);
      } else {
        reject(new Error(result.error));
      }
    });

    // Handle worker errors
    worker.on('error', (error) => {
      reject(error);
    });

    // Handle worker exit
    worker.on('exit', (code) => {
      if (code !== 0) {
        reject(new Error(`Worker stopped with exit code ${code}`));
      }
    });

    // Set timeout if specified
    if (options.timeout) {
      setTimeout(() => {
        worker.terminate();
        reject(new Error('Worker timeout'));
      }, options.timeout);
    }
  });
}

/**
 * Worker thread execution (runs in separate thread)
 */
if (!isMainThread) {
  const { scriptPath, args, options } = workerData;

  // Execute Python script in worker thread
  executePythonScript(scriptPath, args, options)
    .then(output => {
      parentPort.postMessage({ success: true, output });
    })
    .catch(error => {
      parentPort.postMessage({ success: false, error: error.message });
    });
}

/**
 * Execute Python script
 * @param {string} scriptPath - Path to Python script
 * @param {Array} args - Script arguments
 * @param {Object} options - Execution options
 * @returns {Promise} - Promise that resolves with script output
 */
function executePythonScript(scriptPath, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`[PYTHON-WORKER] Executing: python ${scriptPath} ${args.join(' ')}`);

    const python = spawn('python', [scriptPath, ...args], {
      env: {
        ...process.env,
        PYTHONIOENCODING: 'utf-8',
        PYTHONUNBUFFERED: '1'
      },
      cwd: options.cwd || process.cwd()
    });

    let output = '';
    let error = '';

    python.stdout.on('data', (data) => {
      const text = data.toString('utf8');
      output += text;

      // PERMANENT FIX: Parse structured messages instead of using callback functions
      // This eliminates the function cloning error in worker threads
      if (options.enableProgressUpdates || options.enableRealtimeUpdates) {
        const lines = text.split('\n').filter(line => line.trim());
        lines.forEach(line => {
          // Parse PROGRESS_UPDATE messages
          if (line.startsWith('PROGRESS_UPDATE:')) {
            try {
              const progressData = JSON.parse(line.substring(16));
              console.log(`[PYTHON-WORKER] Progress: ${progressData.percentage}% - ${progressData.message}`);
              // Progress data is captured in output for main thread parsing
            } catch (e) {
              console.log(`[PYTHON-WORKER] Could not parse progress: ${line}`);
            }
          }
          // Parse REALTIME_UPDATE messages
          else if (line.startsWith('REALTIME_UPDATE:')) {
            try {
              const updateData = JSON.parse(line.substring(16));
              console.log(`[PYTHON-WORKER] Real-time: ${updateData.type} - ${updateData.message || 'Update'}`);
              // Real-time data is captured in output for main thread parsing
            } catch (e) {
              console.log(`[PYTHON-WORKER] Could not parse real-time update: ${line}`);
            }
          }
          // Log other output
          else if (line.trim()) {
            console.log(`[PYTHON-WORKER] Output: ${line.trim()}`);
          }
        });
      }
    });

    python.stderr.on('data', (data) => {
      const text = data.toString('utf8');
      error += text;
      
      // Log debug messages
      const lines = text.split('\n').filter(line => line.trim());
      lines.forEach(line => {
        if (line.includes('[DEBUG]')) {
          console.log(`[PYTHON-WORKER-DEBUG] ${line.trim()}`);
        } else if (line.trim()) {
          console.error(`[PYTHON-WORKER-ERROR] ${line.trim()}`);
        }
      });
    });

    python.on('close', (code) => {
      console.log(`[PYTHON-WORKER] Process completed with code: ${code}`);
      
      if (code === 0) {
        resolve(output);
      } else {
        reject(new Error(error || `Process exited with code ${code}`));
      }
    });

    python.on('error', (err) => {
      console.error(`[PYTHON-WORKER] Process error: ${err.message}`);
      reject(err);
    });
  });
}

/**
 * Check if worker threads are supported
 * @returns {boolean} - True if worker threads are supported
 */
function isWorkerThreadsSupported() {
  try {
    require('worker_threads');
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Get optimal number of worker threads
 * @returns {number} - Optimal number of workers
 */
function getOptimalWorkerCount() {
  const cpuCount = require('os').cpus().length;
  // Use half of available CPUs, minimum 1, maximum 4
  return Math.max(1, Math.min(4, Math.floor(cpuCount / 2)));
}

/**
 * Worker pool for managing multiple workers
 */
class PythonWorkerPool {
  constructor(maxWorkers = getOptimalWorkerCount()) {
    this.maxWorkers = maxWorkers;
    this.activeWorkers = new Set();
    this.queue = [];
  }

  /**
   * Execute script using worker pool
   * @param {string} scriptPath - Path to Python script
   * @param {Array} args - Script arguments
   * @param {Object} options - Execution options
   * @returns {Promise} - Promise that resolves with script output
   */
  async execute(scriptPath, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      const task = { scriptPath, args, options, resolve, reject };

      if (this.activeWorkers.size < this.maxWorkers) {
        this.executeTask(task);
      } else {
        this.queue.push(task);
      }
    });
  }

  /**
   * Execute a task
   * @param {Object} task - Task to execute
   */
  async executeTask(task) {
    const { scriptPath, args, options, resolve, reject } = task;

    try {
      const workerId = Date.now() + Math.random();
      this.activeWorkers.add(workerId);

      const result = await createPythonWorker(scriptPath, args, options);
      
      this.activeWorkers.delete(workerId);
      resolve(result);

      // Process next task in queue
      if (this.queue.length > 0) {
        const nextTask = this.queue.shift();
        this.executeTask(nextTask);
      }

    } catch (error) {
      reject(error);
    }
  }

  /**
   * Get pool status
   * @returns {Object} - Pool status information
   */
  getStatus() {
    return {
      maxWorkers: this.maxWorkers,
      activeWorkers: this.activeWorkers.size,
      queuedTasks: this.queue.length,
      isSupported: isWorkerThreadsSupported()
    };
  }
}

// Create default worker pool
const defaultWorkerPool = new PythonWorkerPool();

module.exports = {
  createPythonWorker,
  executePythonScript,
  isWorkerThreadsSupported,
  getOptimalWorkerCount,
  PythonWorkerPool,
  defaultWorkerPool
};
