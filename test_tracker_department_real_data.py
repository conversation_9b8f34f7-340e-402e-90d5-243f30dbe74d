#!/usr/bin/env python3
"""
Test Tracker Department with Real Data
Insert real department data into loans & allowance tracker tables and verify display
"""

import os
import sqlite3
from datetime import datetime

def test_tracker_department_insertion():
    """Test inserting records with real department data into tracker tables"""
    
    print("[TEST] TRACKER DEPARTMENT REAL DATA INSERTION")
    print("=" * 60)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute("SELECT session_id FROM sessions ORDER BY created_at DESC LIMIT 1")
        session_result = cursor.fetchone()
        current_session = session_result[0] if session_result else 'test_session'
        
        print(f"[INFO] Using session: {current_session}")
        
        # 1. Test In-House Loans with Real Departments
        print("\n1. TESTING IN-HOUSE LOANS WITH REAL DEPARTMENTS:")
        
        real_loan_data = [
            {
                'employee_no': 'COP001',
                'employee_name': 'OFFICER JOHN MENSAH',
                'department': 'POLICE DEPARTMENT',
                'loan_type': 'STAFF LOAN',
                'loan_amount': 15000.00,
                'period_month': 'June',
                'period_year': 2025
            },
            {
                'employee_no': 'PW002',
                'employee_name': 'ENGINEER MARY ASANTE',
                'department': 'PUBLIC WORKS DEPARTMENT',
                'loan_type': 'EMERGENCY LOAN',
                'loan_amount': 8000.00,
                'period_month': 'June',
                'period_year': 2025
            },
            {
                'employee_no': 'MIN003',
                'employee_name': 'DIRECTOR KWAME OSEI',
                'department': 'MINISTRY OF FINANCE',
                'loan_type': 'HOUSING LOAN',
                'loan_amount': 50000.00,
                'period_month': 'June',
                'period_year': 2025
            }
        ]
        
        for loan in real_loan_data:
            cursor.execute("""
                INSERT INTO in_house_loans 
                (employee_no, employee_name, department, loan_type, loan_amount, 
                 period_month, period_year, period_acquired, source_session, remarks)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                loan['employee_no'], loan['employee_name'], loan['department'],
                loan['loan_type'], loan['loan_amount'], loan['period_month'],
                loan['period_year'], f"{loan['period_month']} {loan['period_year']}",
                current_session, 'Real department test data'
            ))
            
            print(f"   [INSERTED] {loan['employee_no']}: {loan['employee_name']} - {loan['department']}")
        
        # 2. Test Motor Vehicle Maintenance with Real Departments
        print("\n2. TESTING MOTOR VEHICLE MAINTENANCE WITH REAL DEPARTMENTS:")
        
        real_vehicle_data = [
            {
                'employee_no': 'COP004',
                'employee_name': 'SERGEANT AKOSUA BOATENG',
                'department': 'POLICE DEPARTMENT',
                'allowance_type': 'MOTOR VEH. MAINTENAN',
                'allowance_amount': 1200.00,
                'period_month': 'June',
                'period_year': 2025
            },
            {
                'employee_no': 'ENG005',
                'employee_name': 'CHIEF ENGINEER KOFI ADJEI',
                'department': 'ENGINEERING DEPARTMENT',
                'allowance_type': 'VEHICLE MAINTENANCE',
                'allowance_amount': 1500.00,
                'period_month': 'June',
                'period_year': 2025
            }
        ]
        
        for vehicle in real_vehicle_data:
            cursor.execute("""
                INSERT INTO motor_vehicle_maintenance 
                (employee_no, employee_name, department, allowance_type, allowance_amount, 
                 payable_amount, maintenance_amount, period_month, period_year, 
                 period_acquired, source_session, remarks)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                vehicle['employee_no'], vehicle['employee_name'], vehicle['department'],
                vehicle['allowance_type'], vehicle['allowance_amount'], vehicle['allowance_amount'],
                vehicle['allowance_amount'], vehicle['period_month'], vehicle['period_year'],
                f"{vehicle['period_month']} {vehicle['period_year']}", current_session,
                'Real department test data'
            ))
            
            print(f"   [INSERTED] {vehicle['employee_no']}: {vehicle['employee_name']} - {vehicle['department']}")
        
        # 3. Test External Loans with Real Departments
        print("\n3. TESTING EXTERNAL LOANS WITH REAL DEPARTMENTS:")
        
        real_external_loan_data = [
            {
                'employee_no': 'HR006',
                'employee_name': 'HR MANAGER ABENA OWUSU',
                'department': 'HUMAN RESOURCES DEPARTMENT',
                'loan_type': 'BANK LOAN',
                'loan_amount': 25000.00,
                'period_month': 'June',
                'period_year': 2025
            }
        ]
        
        for loan in real_external_loan_data:
            cursor.execute("""
                INSERT INTO external_loans 
                (employee_no, employee_name, department, loan_type, loan_amount, 
                 period_month, period_year, period_acquired, source_session, remarks)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                loan['employee_no'], loan['employee_name'], loan['department'],
                loan['loan_type'], loan['loan_amount'], loan['period_month'],
                loan['period_year'], f"{loan['period_month']} {loan['period_year']}",
                current_session, 'Real department test data'
            ))
            
            print(f"   [INSERTED] {loan['employee_no']}: {loan['employee_name']} - {loan['department']}")
        
        conn.commit()
        
        print(f"\n[SUCCESS] Inserted real department data into all tracker tables")
        return True
        
    except Exception as e:
        print(f"[FAIL] Error inserting real department data: {e}")
        return False

def verify_tracker_department_display():
    """Verify that tracker tables now show real department data"""
    
    print("\n[VERIFY] TRACKER DEPARTMENT DISPLAY VERIFICATION")
    print("=" * 60)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        tracker_tables = [
            ('in_house_loans', 'In-House Loans'),
            ('external_loans', 'External Loans'),
            ('motor_vehicle_maintenance', 'Motor Vehicle Maintenance')
        ]
        
        for table_name, display_name in tracker_tables:
            print(f"\n{display_name.upper()}:")
            
            # Get department distribution
            cursor.execute(f"""
                SELECT department, COUNT(*) as count 
                FROM {table_name} 
                GROUP BY department 
                ORDER BY count DESC
            """)
            dept_counts = cursor.fetchall()
            
            if dept_counts:
                print("   Department distribution:")
                for dept, count in dept_counts:
                    print(f"     - {dept}: {count} records")
                
                # Show recent records with real departments
                cursor.execute(f"""
                    SELECT employee_no, employee_name, department, 
                           CASE 
                               WHEN '{table_name}' = 'motor_vehicle_maintenance' THEN allowance_type
                               ELSE loan_type
                           END as type,
                           CASE 
                               WHEN '{table_name}' = 'motor_vehicle_maintenance' THEN allowance_amount
                               ELSE loan_amount
                           END as amount
                    FROM {table_name} 
                    WHERE department NOT IN ('GENERAL DEPARTMENT', 'DEPARTMENT NOT SPECIFIED', 'UNKNOWN DEPARTMENT')
                    ORDER BY id DESC
                    LIMIT 5
                """)
                real_dept_records = cursor.fetchall()
                
                if real_dept_records:
                    print("   Records with real departments:")
                    for emp_no, emp_name, dept, loan_type, amount in real_dept_records:
                        print(f"     - {emp_no}: {emp_name} ({dept}) - {loan_type}: {amount}")
                else:
                    print("   [WARN] No records with real departments found")
            else:
                print("   [INFO] No records found in table")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error verifying department display: {e}")
        return False

def test_department_extraction_logic():
    """Test the department extraction logic with various employee ID patterns"""
    
    print("\n[TEST] DEPARTMENT EXTRACTION LOGIC")
    print("=" * 60)
    
    test_employee_ids = [
        'COP001',    # Should be POLICE DEPARTMENT
        'PW002',     # Should be PUBLIC WORKS DEPARTMENT
        'MIN003',    # Should be MINISTRY DEPARTMENT
        'ENG004',    # Should be ENGINEERING DEPARTMENT
        'HR005',     # Should be HUMAN RESOURCES DEPARTMENT
        'FIN006',    # Should be FINANCE DEPARTMENT
        'EMP007',    # Should be GENERAL DEPARTMENT
        'ADMIN008',  # Should be ADMINISTRATION DEPARTMENT
        'IT009',     # Should be INFORMATION TECHNOLOGY DEPARTMENT
        'UNKNOWN'    # Should be DEPARTMENT NOT SPECIFIED
    ]
    
    def extract_department_from_employee_id(employee_id):
        """Extract department based on employee ID patterns"""
        if not employee_id:
            return 'DEPARTMENT NOT SPECIFIED'
        
        employee_id = str(employee_id).upper()
        
        # Department mapping based on employee ID patterns
        if employee_id.startswith('COP'):
            return 'POLICE DEPARTMENT'
        elif employee_id.startswith('PW'):
            return 'PUBLIC WORKS DEPARTMENT'
        elif employee_id.startswith('MIN'):
            return 'MINISTRY DEPARTMENT'
        elif employee_id.startswith('ENG'):
            return 'ENGINEERING DEPARTMENT'
        elif employee_id.startswith('HR'):
            return 'HUMAN RESOURCES DEPARTMENT'
        elif employee_id.startswith('FIN'):
            return 'FINANCE DEPARTMENT'
        elif employee_id.startswith('ADMIN'):
            return 'ADMINISTRATION DEPARTMENT'
        elif employee_id.startswith('IT'):
            return 'INFORMATION TECHNOLOGY DEPARTMENT'
        elif employee_id.startswith('EMP'):
            return 'GENERAL DEPARTMENT'
        else:
            return 'DEPARTMENT NOT SPECIFIED'
    
    print("   Testing department extraction:")
    for emp_id in test_employee_ids:
        dept = extract_department_from_employee_id(emp_id)
        print(f"     {emp_id} -> {dept}")
    
    return True

def main():
    """Main function to test tracker department functionality"""
    
    print("[TEST] COMPREHENSIVE TRACKER DEPARTMENT TESTING")
    print("=" * 70)
    
    success = True
    
    # Step 1: Test department extraction logic
    if not test_department_extraction_logic():
        success = False
    
    # Step 2: Insert real department data
    if not test_tracker_department_insertion():
        success = False
    
    # Step 3: Verify department display
    if not verify_tracker_department_display():
        success = False
    
    if success:
        print("\n[SUCCESS] All tracker department tests completed!")
        print("Tracker tables should now show:")
        print("  - Real department names instead of 'department not specified'")
        print("  - Proper department distribution across different departments")
        print("  - Accurate department extraction from employee IDs")
    else:
        print("\n[WARN] Some tracker department tests had issues")
    
    return success

if __name__ == "__main__":
    main()
