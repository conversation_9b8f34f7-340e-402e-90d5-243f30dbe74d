const { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ipc<PERSON>ain, dialog } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

// Worker Threads for non-blocking Python processing
const { Worker } = require('worker_threads');
const os = require('os');
const { createPythonWorker } = require('./python-worker');

// Fast Database Integration
const DatabaseManager = require('./core/database_manager');

// Enhanced IPC Handlers - loaded after ipcMain is available

// Global process tracking for stop functionality
let currentProcesses = new Map();
let pausedProcesses = new Map();

// Worker thread tracking
let activeWorkers = new Map();

// Database integration manager
let databaseManager = null;

// Feature flag for worker threads (can be toggled for testing)
// CRITICAL FIX: Force enable worker threads to prevent UI freezing
let USE_WORKER_THREADS = true; // Always enabled to prevent UI blocking

// Settings file path (legacy - now using database)
// Settings now stored in database - SETTINGS_PATH deprecated

// ============================================================================
// SAFE IPC COMMUNICATION HELPER
// ============================================================================

/**
 * Safely send IPC message to renderer, handling destroyed objects
 */
function safeSendToRenderer(event, channel, data) {
  try {
    if (event && event.sender && !event.sender.isDestroyed()) {
      event.sender.send(channel, data);
      return true;
    } else {
      console.log(`[SAFE-IPC] Skipping ${channel} - renderer destroyed`);
      return false;
    }
  } catch (senderError) {
    console.log(`[SAFE-IPC] Sender error for ${channel} (renderer likely closed): ${senderError.message}`);
    return false;
  }
}

// Keep a global reference of the window object
let mainWindow;
let backendProcess = null;

// Comparison data cache to optimize performance
let comparisonDataCache = new Map();

async function createWindow() {
  console.log('Creating THE PAYROLL AUDITOR main window...');

  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets', 'icon.ico'),
    backgroundColor: '#1a237e',
    show: true,
    title: 'THE PAYROLL AUDITOR'
  });

  // Set global reference for progress updates
  currentMainWindow = mainWindow;

  // Initialize fast database system (optimized for quick startup)
  await initializeFastDatabase();

  // Load the main HTML file
  const indexPath = path.join(__dirname, 'index.html');
  console.log(`Loading index.html from: ${indexPath}`);

  if (fs.existsSync(indexPath)) {
    mainWindow.loadFile(indexPath);
    console.log('Main window loaded successfully');
  } else {
    console.error('index.html not found!');
  }

  // Open DevTools in development
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }

  // Handle window closed
  mainWindow.on('closed', async () => {
    mainWindow = null;
    if (backendProcess) {
      backendProcess.kill();
    }

    // Close database connection
    if (databaseManager) {
      await DatabaseManager.close();
    }
  });
}

/**
 * Initialize fast database system (optimized for quick startup)
 */
async function initializeFastDatabase() {
  try {
    // Use fast singleton database manager for instant startup
    databaseManager = await DatabaseManager.getInstance();

    // Send ready signal to frontend
    if (mainWindow) {
      mainWindow.webContents.send('database-initialized', {
        success: true,
        message: 'Database ready'
      });
    }

  } catch (error) {
    console.error('❌ Fast database initialization error:', error);
  }
}

// App event handlers
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC Handlers for Perfect Section-Aware Extractor with Hybrid Fallback

ipcMain.handle('select-pdf-file', async () => {
  try {
    console.log('[FILE-DIALOG] Opening PDF file selection dialog...');

    // Add timeout to prevent hanging
    const dialogPromise = dialog.showOpenDialog(mainWindow, {
      properties: ['openFile'],
      filters: [
        { name: 'PDF Files', extensions: ['pdf'] },
        { name: 'All Files', extensions: ['*'] }
      ],
      title: 'Select PDF File'
    });

    // Set a timeout of 60 seconds for the dialog
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('File dialog timeout')), 60000);
    });

    const result = await Promise.race([dialogPromise, timeoutPromise]);

    console.log('[FILE-DIALOG] Dialog result:', {
      canceled: result.canceled,
      fileCount: result.filePaths?.length || 0
    });

    if (result.canceled || result.filePaths.length === 0) {
      console.log('[FILE-DIALOG] No file selected or dialog canceled');
      return null;
    }

    const selectedFile = result.filePaths[0];
    console.log('[FILE-DIALOG] File selected:', selectedFile);
    return selectedFile;

  } catch (error) {
    console.error('[FILE-DIALOG] Error in file selection:', error);
    throw error;
  }
});

ipcMain.handle('open-file', async (event, filePath) => {
  try {
    console.log(`[OPEN-FILE] Attempting to open: ${filePath}`);

    // Normalize the path to handle any relative path issues
    const normalizedPath = path.resolve(filePath);
    console.log(`[OPEN-FILE] Normalized path: ${normalizedPath}`);

    // Check if file exists
    if (!fs.existsSync(normalizedPath)) {
      console.error(`[OPEN-FILE] File does not exist: ${normalizedPath}`);
      return { success: false, error: `File not found: ${normalizedPath}` };
    }

    const { shell } = require('electron');
    const result = await shell.openPath(normalizedPath);

    if (result) {
      console.error(`[OPEN-FILE] Shell error: ${result}`);
      return { success: false, error: `Failed to open file: ${result}` };
    }

    console.log(`[OPEN-FILE] Successfully opened: ${normalizedPath}`);
    return { success: true };

  } catch (error) {
    console.error(`[OPEN-FILE] Error opening file: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Open file location (folder containing the file)
ipcMain.handle('open-file-location', async (event, filePath) => {
  try {
    console.log(`[OPEN-FILE-LOCATION] Attempting to open location: ${filePath}`);

    // Normalize the path
    const normalizedPath = path.resolve(filePath);
    console.log(`[OPEN-FILE-LOCATION] Normalized path: ${normalizedPath}`);

    // Check if file exists
    if (!fs.existsSync(normalizedPath)) {
      console.error(`[OPEN-FILE-LOCATION] File does not exist: ${normalizedPath}`);
      return { success: false, error: `File not found: ${normalizedPath}` };
    }

    const { shell } = require('electron');

    // Show the file in its folder
    shell.showItemInFolder(normalizedPath);

    console.log(`[OPEN-FILE-LOCATION] Successfully opened location: ${normalizedPath}`);
    return { success: true };

  } catch (error) {
    console.error(`[OPEN-FILE-LOCATION] Error opening file location: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('extract-payroll-data', async (event, pdfPath, options = {}) => {
  try {
    console.log(`[PERFECT-ONLY] Starting Perfect Section-Aware extraction: ${pdfPath}`);
    console.log(`[PERFECT-ONLY] Worker threads enabled: ${USE_WORKER_THREADS}`);

    // Use ONLY Perfect Section-Aware Extractor (98-100% accuracy, no fallback needed)
    const pythonPath = path.join(__dirname, 'core', 'perfect_extraction_integration.py');
    const args = ['extract', pdfPath];

    // Add page number if provided
    if (options.page_num) args.push(options.page_num.toString());

    // Use smart router to choose between worker thread and main thread
    const result = await runPythonScriptSmart(pythonPath, args, event, {
      timeout: 30 * 60 * 1000 // 30 minutes timeout
    });

    console.log(`[PERFECT-ONLY] Perfect Section-Aware extraction completed with 98-100% accuracy`);
    return JSON.parse(result);
  } catch (error) {
    console.error(`[PERFECT-ONLY] Perfect Section-Aware extraction failed: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Process-payroll handler using Perfect-Only extraction
ipcMain.handle('process-payroll', async (event, pdfPath, options = {}) => {
  try {
    console.log(`[PERFECT-ONLY] Starting Perfect payroll processing: ${pdfPath}`);

    // Use ONLY Perfect Section-Aware Extractor (98-100% accuracy)
    const pythonPath = path.join(__dirname, 'core', 'perfect_extraction_integration.py');
    const args = ['extract', pdfPath];

    // Add page number if provided
    if (options.page_num) args.push(options.page_num.toString());

    // CRITICAL FIX: Use non-blocking worker thread to prevent UI freezing
    const result = await runPythonScriptSmart(pythonPath, args, event, { timeout: 30 * 60 * 1000 });

    console.log(`[PERFECT-ONLY] Perfect payroll processing completed with 98-100% accuracy`);
    return JSON.parse(result);
  } catch (error) {
    console.error(`[PERFECT-ONLY] Perfect payroll processing failed: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// New handler for real-time extraction with live updates
function runPythonScriptWithRealTimeUpdates(scriptPath, args = [], event, callbacks = {}) {
  return new Promise((resolve, reject) => {
    // Check if arguments are too long (total command length > 8000 chars)
    const commandLength = `python ${scriptPath} ${args.join(' ')}`.length;
    
    // If command is too long, use a temporary file for arguments
    let argsToUse = args;
    let tempArgsFile = null;
    
    if (commandLength > 7000) {
      try {
        const fs = require('fs');
        const os = require('os');
        const crypto = require('crypto');
        
        // Create a unique hash for this command
        const hash = crypto.createHash('md5')
          .update(args.join('|') + Date.now().toString())
          .digest('hex')
          .substring(0, 10);
          
        // Create a temporary file with the arguments as JSON
        tempArgsFile = path.join(os.tmpdir(), `payroll_args_${hash}.json`);
        
        // Write args to temp file
        fs.writeFileSync(
          tempArgsFile,
          JSON.stringify(args),
          'utf8'
        );
        
        // Replace all arguments with a single argument pointing to the JSON file
        argsToUse = ['--args-file', tempArgsFile];
        console.log(`[PYTHON-REALTIME] Using temp file for long arguments: ${tempArgsFile}`);
      } catch (err) {
        console.error(`[PYTHON-REALTIME] Error creating temp args file: ${err.message}`);
        // Fall back to original args if temp file creation fails
        argsToUse = args;
      }
    }
    
    console.log(`[PYTHON-REALTIME] Starting Python process: python ${scriptPath} ${argsToUse.length > 5 ? '(args too long to display)' : argsToUse.join(' ')}`);
    
    const python = spawn('python', [scriptPath, ...argsToUse], {
      env: {
        ...process.env,
        PYTHONIOENCODING: 'utf-8',
        PYTHONUNBUFFERED: '1'  // Force unbuffered output
      }
    });
    
    // Track process for stop functionality
    const processId = Date.now();
    currentProcesses.set(processId, python);
    console.log(`[PROCESS-TRACK] Tracking process ${processId} for stop functionality`);

    let finalOutput = '';
    let error = '';
    
    // Set up stdout data handling
    let stdoutData = '';
    let dataLines = [];
    // Buffer for collecting partial real-time update messages
    let realtimeUpdateBuffer = '';
    
    // Make sure finalOutput is properly accumulated from all relevant stdout data

    python.stdout.on('data', (data) => {
      try {
        const newData = data.toString();
        stdoutData += newData;
        
        // Process any complete lines
        const lines = stdoutData.split('\n');
        // Keep the last (potentially incomplete) line in the buffer
        stdoutData = lines.pop() || '';
        
        // Add complete lines to our collection
        if (lines.length > 0) {
          dataLines.push(...lines);
          
          // Process each line
          lines.forEach(line => {
            if (line.trim() !== '') {
              // Check if this is a REALTIME_UPDATE line
              if (line.includes('REALTIME_UPDATE:')) {
                // Add to realtime buffer - might be split across multiple chunks
                realtimeUpdateBuffer += line;
                
                // Try to extract a complete JSON object
                if (realtimeUpdateBuffer.includes('{') && realtimeUpdateBuffer.includes('}')) {
                  // Only process if it looks like a complete message
                  const jsonStart = realtimeUpdateBuffer.indexOf('{');
                  const jsonEnd = realtimeUpdateBuffer.lastIndexOf('}');
                  
                  if (jsonStart !== -1 && jsonEnd !== -1 && jsonStart < jsonEnd) {
                    // This could be a complete message, send it for parsing
                    if (event) {
                      parseRealTimeExtractionUpdate(realtimeUpdateBuffer, event);
                    }
                    // Clear the buffer after processing
                    realtimeUpdateBuffer = '';
                  }
                }
                
                // If buffer gets too large, clear it to prevent memory issues
                if (realtimeUpdateBuffer.length > 10000) {
                  console.warn('[PYTHON-PROCESS] Realtime update buffer too large, clearing');
                  realtimeUpdateBuffer = '';
                }
              } else {
                // Regular line, process normally
                if (event) {
                  // Parse and process as normal output
                  parseRealTimeExtractionUpdate(line, event);
                }
                
                // Add to finalOutput for JSON parsing at completion
                finalOutput += line + '\n';
              }
              
              // Only call onStdout callback if it exists
              if (callbacks && typeof callbacks.onStdout === 'function') {
                callbacks.onStdout(line);
              }
            }
          });
        }
      } catch (dataError) {
        console.error('[PYTHON-PROCESS] Error processing stdout data:', dataError);
      }
    });

    python.stderr.on('data', (data) => {
      const text = data.toString('utf8');
      error += text;

      // Parse stderr for debug messages and errors
      const lines = text.split('\n').filter(line => line.trim());
      lines.forEach(line => {
        if (line.includes('[DEBUG]')) {
          console.log(`[DEBUG-REALTIME] ${line.trim()}`);
          // Only parse debug messages that are real-time updates
          if (line.includes('REALTIME_UPDATE:')) {
            parseRealTimeExtractionUpdate(line.trim(), event);
          }
        } else if (line.trim()) {
          console.error(`[ERROR-REALTIME] ${line.trim()}`);
        }
      });
    });

    python.on('close', (code) => {
      console.log(`[PYTHON-REALTIME] Process ${processId} completed with code: ${code}`);
      currentProcesses.delete(processId);
      
      // Clean up temporary args file if it was created
      if (tempArgsFile) {
        try {
          const fs = require('fs');
          if (fs.existsSync(tempArgsFile)) {
            fs.unlinkSync(tempArgsFile);
            console.log(`[PYTHON-REALTIME] Cleaned up temporary args file: ${tempArgsFile}`);
          }
        } catch (cleanupErr) {
          console.error(`[PYTHON-REALTIME] Error cleaning up temp args file: ${cleanupErr.message}`);
          // Non-fatal error, continue with process
        }
      }

      if (code === 0) {
        // DATABASE-ONLY: Process string-based output - only look at the last line
        try {
          const output = finalOutput.trim();
          console.log(`[PYTHON-REALTIME] Processing final output (${output.length} chars total)`);

          // DATABASE-ONLY: Extract only the last line which should be SUCCESS:session_id or ERROR:message
          const lines = output.split('\n');
          const lastLine = lines[lines.length - 1].trim();

          console.log(`[PYTHON-REALTIME] Last line extracted: ${lastLine}`);

          // Return only the last line for processing by the caller
          resolve(lastLine);
        } catch (outputError) {
          console.error(`[PYTHON-REALTIME] Failed to process output: ${outputError.message}`);
          reject(new Error(`Failed to process output: ${outputError.message}`));
        }
      } else {
        // CRITICAL FIX: Handle null exit codes properly
        const exitCode = code !== null ? code : 'null';
        const errorMessage = error || `Process exited with code ${exitCode}`;
        
        // Don't treat null exit codes as fatal errors if there's output
        if (code === null && finalOutput.trim()) {
          console.warn(`[PYTHON-REALTIME] Process ${processId} exited with null code but has output, treating as success`);
          resolve(finalOutput);
        } else {
          reject(new Error(errorMessage));
        }
      }
    });

    python.on('error', (err) => {
      console.error(`[PYTHON-REALTIME] Process ${processId} error: ${err.message}`);
      currentProcesses.delete(processId);
      reject(err);
    });
  });
}

// Buffer for collecting partial real-time updates
let realTimeUpdateBuffer = '';

// PRODUCTION FIX: Enhanced real-time update parsing with better error handling
function parseRealTimeExtractionUpdate(logLine, event) {
  try {
    // Parse JSON real-time updates from Python
    // Strip the REALTIME_UPDATE: prefix
    if (logLine.startsWith('REALTIME_UPDATE:')) {
      const updateContent = logLine.substring('REALTIME_UPDATE:'.length).trim();

      // PRODUCTION FIX: Enhanced JSON format validation
      if (!updateContent.startsWith('{') || !updateContent.endsWith('}')) {
        // Skip debug messages and non-JSON content
        if (updateContent.includes('[DEBUG]') || updateContent.includes('Raw options JSON')) {
          return; // Silently skip debug messages
        }
        console.log(`[REALTIME-PARSE] Invalid JSON format, skipping: ${updateContent.substring(0, 50)}...`);
        return;
      }

      // PRODUCTION FIX: Direct JSON parsing for better performance
      try {
        const updateData = JSON.parse(updateContent);

        // Only process valid objects
        if (updateData && typeof updateData === 'object') {
          // PRODUCTION FIX: Enhanced update data with progress tracking
          if (updateData.percentage !== undefined) {
            updateData.percentage = Math.min(100, Math.max(0, updateData.percentage));
          }

          // Send real-time update to frontend with error handling
          try {
            if (event && event.sender && !event.sender.isDestroyed()) {
              // Send to both channels for compatibility with new phased process manager
              event.sender.send('realtime-extraction-update', updateData);
              event.sender.send('enhanced-progress-update', updateData);
              const pageInfo = updateData.page_number ? ` [Page ${updateData.page_number}]` : '';
              console.log(`[PHASED-PROGRESS] ${updateData.type}${pageInfo}: ${updateData.message || 'Update sent'}`);
            }
          } catch (senderError) {
            console.log(`[PHASED-PROGRESS] Sender error (renderer likely closed): ${senderError.message}`);
          }
        }
        return;
      } catch (directParseError) {
        // PRODUCTION FIX: Fallback to buffer-based parsing for multi-line JSON
        console.log(`[REALTIME-PARSE] Direct parse failed, using buffer: ${directParseError.message.substring(0, 100)}`);

        // Add this line to our buffer - it might be part of a multi-line or truncated message
        realTimeUpdateBuffer += updateContent;

        try {
          // Attempt to find complete JSON objects in the buffer
          let jsonStart = -1;
          let bracketCount = 0;
          let lastCompleteEndPos = -1;

          // Look for potentially complete JSON objects in our buffer
          for (let i = 0; i < realTimeUpdateBuffer.length; i++) {
            const char = realTimeUpdateBuffer[i];

            // Mark potential start of a JSON object
            if (char === '{' && bracketCount === 0) {
              jsonStart = i;
            }

            // Track bracket depth
            if (char === '{') bracketCount++;
            if (char === '}') bracketCount--;

            // If we've found a complete object, process it
            if (jsonStart !== -1 && bracketCount === 0 && char === '}') {
              const jsonObject = realTimeUpdateBuffer.substring(jsonStart, i + 1);

              try {
                // Try to parse the complete JSON object
                const updateData = JSON.parse(jsonObject);

                // Only process valid objects
                if (updateData && typeof updateData === 'object') {
                  // PRODUCTION FIX: Enhanced update data with progress tracking
                  if (updateData.percentage !== undefined) {
                    updateData.percentage = Math.min(100, Math.max(0, updateData.percentage));
                  }

                  // Send real-time update to frontend with error handling
                  try {
                    if (event && event.sender && !event.sender.isDestroyed()) {
                      // Send to both channels for compatibility with new phased process manager
                      event.sender.send('realtime-extraction-update', updateData);
                      event.sender.send('enhanced-progress-update', updateData);
                      const pageInfo = updateData.page_number ? ` [Page ${updateData.page_number}]` : '';
                      console.log(`[PHASED-PROGRESS] ${updateData.type}${pageInfo}: ${updateData.message || 'Update sent'}`);
                    }
                  } catch (senderError) {
                    console.log(`[PHASED-PROGRESS] Sender error (renderer likely closed): ${senderError.message}`);
                  }
                }

                // Remember this position as the last successful parse
                lastCompleteEndPos = i + 1;
              } catch (objParseError) {
                // This particular object was invalid - log and continue
                console.error('[REALTIME-PARSE] Invalid JSON object in buffer:', objParseError);
                console.error('[REALTIME-PARSE] Problematic object:', jsonObject.substring(0, 100) + '...');
              }
            }
          }

          // Clean up the buffer - remove any successfully processed content
          if (lastCompleteEndPos > 0) {
            realTimeUpdateBuffer = realTimeUpdateBuffer.substring(lastCompleteEndPos).trim();
          }

          // If buffer gets too large (over 8KB), clear it to prevent memory issues
          if (realTimeUpdateBuffer.length > 8192) {
            console.warn('[REALTIME-PARSE] Buffer too large, clearing to prevent memory issues');
            realTimeUpdateBuffer = '';
          }
        } catch (parseError) {
          console.error('[REALTIME-PARSE] Error in real-time update parsing:', parseError);
          console.error('[REALTIME-PARSE] Current buffer state (truncated):',
                        realTimeUpdateBuffer.substring(0, 100) + '...');
          // Continue processing instead of failing
        }
      }
      return;
    }

    // Parse section processing: "Processing section: EARNINGS"
    const sectionMatch = logLine.match(/Processing section: (.+)/);
    if (sectionMatch) {
      const sectionName = sectionMatch[1];
      safeSendToRenderer(event, 'realtime-extraction-update', {
        type: 'section_start',
        section: sectionName,
        message: `Processing ${sectionName} section...`
      });
      return;
    }

    // Parse item found: "Found item: BASIC SALARY = 2500.00"
    const itemMatch = logLine.match(/Found item: (.+) = (.+)/);
    if (itemMatch) {
      const [, label, value] = itemMatch;
      safeSendToRenderer(event, 'realtime-extraction-update', {
        type: 'new_item_found',
        item: {
          label: label.trim(),
          value: value.trim(),
          confidence: 1.0,
          source: '100% Accurate Hybrid Extractor'
        },
        message: `Found: ${label} = ${value}`
      });
      return;
    }

    // Parse extraction start
    if (logLine.includes('Starting hybrid extraction') || logLine.includes('PERFECT SECTION EXTRACTION')) {
      safeSendToRenderer(event, 'realtime-extraction-update', {
        type: 'extraction_start',
        message: 'Starting 100% accurate section-aware extraction...'
      });
      return;
    }

    // Parse extraction complete
    if (logLine.includes('Extraction complete') || logLine.includes('PERFECT EXTRACTION SUMMARY')) {
      safeSendToRenderer(event, 'realtime-extraction-update', {
        type: 'extraction_complete',
        message: 'Extraction completed with 100% accuracy'
      });
      return;
    }

    // Parse batch processing progress
    if (logLine.includes('Processing batch:') || logLine.includes('LARGE PAYROLL PROCESSING')) {
      const batchMatch = logLine.match(/Processing batch: Pages (\d+)-(\d+)/);
      if (batchMatch) {
        const [, startPage, endPage] = batchMatch;
        safeSendToRenderer(event, 'realtime-extraction-update', {
          type: 'batch_progress',
          message: `Processing batch: Pages ${startPage}-${endPage}`
        });
      } else {
        safeSendToRenderer(event, 'realtime-extraction-update', {
          type: 'batch_start',
          message: 'Starting large payroll processing...'
        });
      }
      return;
    }

    // Parse section processing
    if (logLine.includes('SECTION') && (logLine.includes('PERSONAL') || logLine.includes('EARNINGS') || logLine.includes('DEDUCTIONS'))) {
      const sectionMatch = logLine.match(/(PERSONAL DETAILS|EARNINGS|DEDUCTIONS|LOWER SECTIONS)/);
      if (sectionMatch) {
        const sectionName = sectionMatch[1];
        safeSendToRenderer(event, 'realtime-extraction-update', {
          type: 'section_start',
          section: sectionName,
          message: `Processing ${sectionName} section...`
        });
      }
      return;
    }

    // Parse found items
    if (logLine.includes('Found ') && logLine.includes(':')) {
      const foundMatch = logLine.match(/Found (.+): (.+)/);
      if (foundMatch) {
        const [, label, value] = foundMatch;
        safeSendToRenderer(event, 'realtime-extraction-update', {
          type: 'new_item_found',
          item: {
            label: label.trim(),
            value: value.trim(),
            confidence: 1.0,
            source: 'Perfect Section-Aware Extractor'
          },
          message: `Found: ${label} = ${value}`
        });
      }
      return;
    }

    // Parse auto-learning session started
    if (logLine.includes('AUTO-LEARNING SESSION STARTED')) {
      safeSendToRenderer(event, 'realtime-extraction-update', {
        type: 'auto_learning_session_started',
        message: 'Auto-learning session started - Watch items appear in real-time!'
      });
      return;
    }

    // Parse item discoveries
    if (logLine.includes('ITEM DISCOVERED:')) {
      const discoveryMatch = logLine.match(/ITEM DISCOVERED: (.+)\.(.+) = (.+) \(confidence: (.+)\)/);
      if (discoveryMatch) {
        const [, section, label, value, confidence] = discoveryMatch;
        safeSendToRenderer(event, 'realtime-extraction-update', {
          type: 'new_item_discovered',
          discovery: {
            section: section.trim(),
            label: label.trim(),
            value: value.trim(),
            confidence: parseFloat(confidence)
          },
          message: `Auto-Learning: ${section}.${label} = ${value}`
        });
      }
      return;
    }

    // Parse PDF Sorter progress updates: "PROGRESS: {...}"
    if (logLine.startsWith('PROGRESS:')) {
      try {
        const progressJson = logLine.replace('PROGRESS:', '').trim();
        const progressData = JSON.parse(progressJson);

        // Convert PDF Sorter progress to real-time extraction update format
        safeSendToRenderer(event, 'realtime-extraction-update', {
          type: 'pdf_sorter_progress',
          percentage: progressData.percentage || 0,
          message: progressData.message || 'Processing...',
          processed_pages: progressData.processed_pages || 0,
          extracted_payslips: progressData.extracted_payslips || 0,
          current_stage: progressData.current_stage || 'Processing'
        });
      } catch (parseError) {
        console.error('[PDF-SORTER] Error parsing progress JSON:', parseError);
      }
      return;
    }

    // Parse PDF Sorter status messages
    if (logLine.includes('ENTERPRISE PDF SORTER') || logLine.includes('Extracted') && logLine.includes('payslips')) {
      const extractedMatch = logLine.match(/Extracted (\d+) valid payslips/);
      if (extractedMatch) {
        const payslipsCount = parseInt(extractedMatch[1]);
        safeSendToRenderer(event, 'realtime-extraction-update', {
          type: 'pdf_sorter_progress',
          message: `Extracted ${payslipsCount} payslips`,
          extracted_payslips: payslipsCount,
          current_stage: 'Extraction Complete'
        });
      }
      return;
    }

  } catch (error) {
    console.error('[REALTIME-PARSE] Error parsing real-time update:', error);
  }
}

ipcMain.handle('get-system-status', async () => {
  try {
    // Use Perfect-Only extraction system status
    const pythonPath = path.join(__dirname, 'core', 'perfect_extraction_integration.py');
    const result = await runHybridScript(pythonPath, ['status']);
    return JSON.parse(result);
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Perfect-Only Extractor - No settings needed (98-100% accuracy guaranteed)

ipcMain.handle('process-large-payroll', async (event, pdfPath, options = {}) => {
  try {
    // Load batch size from settings or use provided option or default
    const settings = await loadAppSettings();
    const batchSize = options.batchSize || settings?.performance?.batchThreshold || 50;
    const maxPages = options.maxPages || null;

    console.log(`[PERFECT-BATCH] STARTING PERFECT-ONLY LARGE PAYROLL PROCESSING:`);
    console.log(`[PERFECT-BATCH]    PDF: ${pdfPath}`);
    console.log(`[PERFECT-BATCH]    Batch size: ${batchSize}`);
    console.log(`[PERFECT-BATCH]    Max pages: ${maxPages || 'All'}`);
    console.log(`[PERFECT-BATCH]    Expected accuracy: 98-100% per payslip`);

    // Use Perfect-Only Extraction System for Large Payrolls
    const pythonPath = path.join(__dirname, 'core', 'perfect_extraction_integration.py');
    const args = ['batch', pdfPath];

    if (maxPages) args.push('--max-pages', maxPages.toString());
    args.push('--batch-size', batchSize.toString());

    // CRITICAL FIX: Use non-blocking worker thread to prevent UI freezing
    const result = await runPythonScriptSmart(pythonPath, args, event, { timeout: 60 * 60 * 1000 });

    console.log(`[PERFECT-BATCH] PERFECT-ONLY LARGE PAYROLL PROCESSING COMPLETED`);
    return JSON.parse(result);
  } catch (error) {
    console.error(`[PERFECT-BATCH] PERFECT-ONLY LARGE PAYROLL PROCESSING FAILED: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Helper function to create a short path for long file paths
function createShortPathAlias(originalPath) {
  const fs = require('fs');
  const crypto = require('crypto');
  const os = require('os');
  const tmpDir = os.tmpdir();
  
  // Create a hash of the original path
  const hash = crypto.createHash('md5').update(originalPath).digest('hex').substring(0, 10);
  
  // Create a symlink or shortcut in the temp directory
  const shortPath = path.join(tmpDir, `payroll_${hash}.pdf`);
  
  // If the file already exists, remove it (it might be a stale symlink)
  if (fs.existsSync(shortPath)) {
    fs.unlinkSync(shortPath);
  }
  
  // Create a copy of the file with a shorter path
  fs.copyFileSync(originalPath, shortPath);
  
  return shortPath;
}

// Enhanced Payroll Audit handler has been consolidated with the implementation below

ipcMain.handle('enhanced-payroll-audit', async (event, currentPdf, previousPdf, options = {}) => {
  let shortCurrentPath = null;
  let shortPreviousPath = null;

  try {
    console.log(`[DATABASE-ONLY-AUDIT] STARTING DATABASE-ONLY PAYROLL AUDIT PROCESS:`);
    console.log(`[DATABASE-ONLY-AUDIT]    Current PDF: ${currentPdf}`);
    console.log(`[DATABASE-ONLY-AUDIT]    Previous PDF: ${previousPdf}`);
    console.log(`[DATABASE-ONLY-AUDIT]    Worker threads enabled: ${USE_WORKER_THREADS}`);

    // Check if the paths are too long and create shorter aliases if needed
    if (currentPdf.length > 180) {
      shortCurrentPath = createShortPathAlias(currentPdf);
      console.log(`[ENHANCED-AUDIT] Using shorter path alias for current PDF: ${shortCurrentPath}`);
    } else {
      shortCurrentPath = currentPdf;
    }

    if (previousPdf.length > 180) {
      shortPreviousPath = createShortPathAlias(previousPdf);
      console.log(`[ENHANCED-AUDIT] Using shorter path alias for previous PDF: ${shortPreviousPath}`);
    } else {
      shortPreviousPath = previousPdf;
    }

    // DATABASE-ONLY: Use the Phased Process Manager for database-only execution
    console.log(`[DATABASE-ONLY-AUDIT] Using Database-Only Phased Process Manager`);
    const phasedManagerPath = path.join(__dirname, 'core', 'phased_process_manager.py');

    // Prepare options for phased process manager
    const phasedOptions = {
      current_month: options.currentMonth || new Date().getMonth() + 1,
      current_year: options.currentYear || new Date().getFullYear(),
      previous_month: options.previousMonth || (new Date().getMonth() === 0 ? 12 : new Date().getMonth()),
      previous_year: options.previousYear || (new Date().getMonth() === 0 ? new Date().getFullYear() - 1 : new Date().getFullYear()),
      signature_name: options.signatureName || 'System Administrator',
      signature_designation: options.signatureDesignation || 'Payroll Auditor',
      report_type: options.reportType || 'standard',
      ...options
    };

    // DATABASE-ONLY: Call the NEW Phased Process Manager with proper workflow
    // CRITICAL FIX: Use non-blocking worker thread to prevent UI freezing
    const phasedResult = await runPythonScriptSmart(
      path.join(__dirname, 'core', 'phased_process_manager.py'),
      ['execute-workflow', shortCurrentPath, shortPreviousPath, JSON.stringify(phasedOptions)],
      event,
      { timeout: 60 * 60 * 1000 } // 60 minutes timeout for large payroll processing
    );

    // DATABASE-ONLY: Process the non-JSON result (session ID + status only)
    let phasedData;
    try {
      console.log(`[DATABASE-ONLY-AUDIT] Processing database-only result: ${typeof phasedResult}`);

      // Handle string-based result format: "SUCCESS:session_id", "ERROR:message", or "WAITING_FOR_USER:session_id"
      if (typeof phasedResult === 'string') {
        const resultStr = phasedResult.trim();

        if (resultStr.startsWith('SUCCESS:')) {
          const sessionId = resultStr.substring(8); // Remove "SUCCESS:" prefix
          phasedData = {
            success: true,
            session_id: sessionId,
            status: 'completed'
          };
          console.log(`[DATABASE-ONLY-AUDIT] Successfully processed database-only result, session: ${sessionId}`);
        } else if (resultStr.startsWith('WAITING_FOR_USER:')) {
          const sessionId = resultStr.substring(17); // Remove "WAITING_FOR_USER:" prefix
          phasedData = {
            success: true,
            session_id: sessionId,
            status: 'waiting_for_user'
          };
          console.log(`[DATABASE-ONLY-AUDIT] Process waiting for user interaction, session: ${sessionId}`);
        } else if (resultStr.startsWith('ERROR:')) {
          const errorMessage = resultStr.substring(6); // Remove "ERROR:" prefix
          throw new Error(`Database-only process failed: ${errorMessage}`);
        } else {
          throw new Error(`Invalid result format: ${resultStr}`);
        }
      } else {
        console.error(`[DATABASE-ONLY-AUDIT] Unexpected result format: ${typeof phasedResult}`);
        throw new Error(`Database-only result is not valid: ${typeof phasedResult}`);
      }
    } catch (parseError) {
      console.error(`[DATABASE-ONLY-AUDIT] Failed to process result: ${parseError.message}`);
      throw new Error(`Failed to process database-only results: ${parseError.message}`);
    }

    // Check if the phased process was successful
    if (!phasedData.success) {
      throw new Error(`Database-only phased process failed: ${phasedData.error || 'Unknown error'}`);
    }

    // CRITICAL FIX: Handle WAITING_FOR_USER status properly
    if (phasedData.status === 'waiting_for_user') {
      console.log(`[DATABASE-ONLY-AUDIT] Process waiting for user interaction, session: ${phasedData.session_id}`);

      // Notify UI to switch to pre-reporting phase
      safeSendToRenderer(event, 'backend-progress', {
        phase: 'PRE_REPORTING',
        progress: 80,
        message: 'waiting_for_user',
        session_id: phasedData.session_id,
        timestamp: new Date().toISOString()
      });

      // Return success with waiting status - this is NOT an error
      return {
        success: true,
        status: 'waiting_for_user',
        session_id: phasedData.session_id,
        message: 'Process completed successfully, waiting for user interaction'
      };
    }

    // DATABASE-ONLY: Load comparison results from database using session ID
    console.log(`[DATABASE-ONLY-AUDIT] Loading comparison results from database for session: ${phasedData.session_id}`);
    const comparisonResults = await loadComparisonResultsFromDatabase(phasedData.session_id);

    // Notify UI that the complete audit process is finished
    safeSendToRenderer(event, 'audit-complete', {
      success: true,
      sessionId: phasedData.session_id,
      stats: { totalChanges: comparisonResults.length },
      timestamp: new Date().toISOString()
    });

    // DATABASE-ONLY: All phases are now handled by the Database-Only Phased Process Manager
    // The database-only phased process manager handles:
    // - Phase 1: Data Extraction → Database Storage
    // - Phase 2: Comparison → Database Storage
    // - Phase 3: Tracker Feeding → Database Storage
    // - Phase 4: Auto Learning → Database Storage
    // - Phase 5: Pre-reporting → Database Storage
    // - Phase 6: Report Generation → Database Storage

    console.log(`[DATABASE-ONLY-AUDIT] All phases completed by Database-Only Phased Process Manager`);

    // Process prioritized changes from the database results
    const priorizedChanges = processPrioritizedChanges(comparisonResults || []);

    // DATABASE-ONLY: Send pre-reporting data ready event with results from database
    safeSendToRenderer(event, 'pre-reporting-data-ready', {
      success: true,
      stats: {
        highPriorityChanges: priorizedChanges.filter(c => c.priority === 'High').length,
        mediumPriorityChanges: priorizedChanges.filter(c => c.priority === 'Medium').length,
        lowPriorityChanges: priorizedChanges.filter(c => c.priority === 'Low').length,
        routineChanges: priorizedChanges.filter(c => c.routine === true).length,
        totalChanges: priorizedChanges.length
      },
      changes: priorizedChanges,
      sessionId: phasedData.session_id,
      timestamp: new Date().toISOString()
    });

    // Handle different completion statuses
    if (phasedData.status === 'waiting_for_user') {
      console.log(`[DATABASE-ONLY-AUDIT] PROCESS WAITING FOR USER INTERACTION`);

      // Send waiting for user event to UI
      safeSendToRenderer(event, 'process-waiting-for-user', {
        success: true,
        status: 'waiting_for_user',
        phase: 'PRE_REPORTING',
        sessionId: phasedData.session_id,
        message: 'PRE-REPORTING phase ready for user interaction',
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        status: 'waiting_for_user',
        message: 'Process waiting for user interaction in PRE-REPORTING phase',
        sessionId: phasedData.session_id,
        phase: 'PRE_REPORTING'
      };
    } else {
      console.log(`[DATABASE-ONLY-AUDIT] DATABASE-ONLY PHASED PROCESS COMPLETED SUCCESSFULLY`);
    }

    // Clean up temporary file aliases if they were created
    try {
      const fs = require('fs');
      if (shortCurrentPath !== currentPdf && fs.existsSync(shortCurrentPath)) {
        fs.unlinkSync(shortCurrentPath);
        console.log(`[ENHANCED-AUDIT] Cleaned up temporary path alias: ${shortCurrentPath}`);
      }
      if (shortPreviousPath !== previousPdf && fs.existsSync(shortPreviousPath)) {
        fs.unlinkSync(shortPreviousPath);
        console.log(`[ENHANCED-AUDIT] Cleaned up temporary path alias: ${shortPreviousPath}`);
      }
    } catch (cleanupError) {
      console.error(`[ENHANCED-AUDIT] Error cleaning up temporary files: ${cleanupError.message}`);
      // Non-fatal error, continue with success response
    }

    return {
      success: true,
      status: 'completed',
      message: 'Database-only phased payroll audit process executed successfully',
      sessionId: phasedData.session_id,
      stats: {
        totalChanges: priorizedChanges.length,
        highPriority: priorizedChanges.filter(c => c.priority === 'High').length,
        mediumPriority: priorizedChanges.filter(c => c.priority === 'Medium').length,
        lowPriority: priorizedChanges.filter(c => c.priority === 'Low').length,
        routineChanges: priorizedChanges.filter(c => c.routine === true).length
      },
      databaseArchitecture: true
    };
  } catch (error) {
    console.error(`[ENHANCED-AUDIT] WORKFLOW FAILED: ${error.message}`);
    
    // Clean up any temporary files that might have been created
    try {
      const fs = require('fs');
      // Clean up temporary path aliases if they were created
      if (shortCurrentPath && shortCurrentPath !== currentPdf && fs.existsSync(shortCurrentPath)) {
        fs.unlinkSync(shortCurrentPath);
        console.log(`[ENHANCED-AUDIT] Cleaned up temporary path alias on error: ${shortCurrentPath}`);
      }
      if (shortPreviousPath && shortPreviousPath !== previousPdf && fs.existsSync(shortPreviousPath)) {
        fs.unlinkSync(shortPreviousPath);
        console.log(`[ENHANCED-AUDIT] Cleaned up temporary path alias on error: ${shortPreviousPath}`);
      }
    } catch (cleanupError) {
      console.error(`[ENHANCED-AUDIT] Error during cleanup after workflow failure: ${cleanupError.message}`);
      // Non-fatal error, continue with error response
    }
    
    // Reset progress status on error
    safeSendToRenderer(event, 'backend-progress', {
      phase: 'error',
      progress: 0,
      error: error.message,
      timestamp: new Date().toISOString()
    });
    
    return { success: false, error: error.message };
  }
});

/**
 * DATABASE-ONLY: Load comparison results from database using session ID
 * @param {string} sessionId - The session ID from phased process
 * @returns {Array} - Array of comparison results from database
 */
async function loadComparisonResultsFromDatabase(sessionId) {
  try {
    if (!databaseManager || !databaseManager.isInitialized) {
      console.error('[DATABASE-ONLY] Database not initialized');
      return [];
    }

    // Load comparison results from database
    const comparisonResults = await databaseManager.getAllQuery(
      'SELECT * FROM comparison_results WHERE session_id = ? ORDER BY priority DESC, employee_id',
      [sessionId]
    );

    console.log(`[DATABASE-ONLY] Loaded ${comparisonResults.length} comparison results from database`);

    // Convert database format to expected format
    return comparisonResults.map(result => ({
      employee_id: result.employee_id,
      employee_name: result.employee_name,
      department: result.department || 'UNKNOWN DEPARTMENT',
      section: result.section_name,
      item: result.item_label,
      item_name: result.item_label,
      previous_value: result.previous_value,
      current_value: result.current_value,
      change_type: result.change_type,
      priority: result.priority,
      numeric_difference: result.numeric_difference,
      percentage_change: result.percentage_change
    }));

  } catch (error) {
    console.error('[DATABASE-ONLY] Error loading comparison results from database:', error);
    return [];
  }
}

/**
 * Process changes according to priority rules and business requirements
 * @param {Array} changes - The detected changes from comparison and auto-learning
 * @returns {Array} - Prioritized changes with correct reporting format
 */
function processPrioritizedChanges(changes) {
  if (!Array.isArray(changes)) return [];
  
  // Section priority mapping based on business requirements
  const sectionPriorities = {
    'PERSONAL DETAILS': 'High',
    'EARNINGS': 'High',
    'DEDUCTIONS': 'High',
    'LOANS': 'Medium',  // Moderate converted to Medium
    'EMPLOYERS CONTRIBUTION': 'Low',
    'EMPLOYEE BANK DETAILS': 'Medium'  // Moderate converted to Medium
  };
  
  // Known routine bulk changes patterns (items to filter out from final report)
  const routinePatterns = [
    'STAFF CREDIT UNION',
    'CREDIT UNION CONTRIBUTION',
    'STAFF SAVINGS',
    'VOLUNTARY DEDUCTION',
    'SEASONAL ADJUSTMENT'
  ];
  
  return changes.map(change => {
    // Apply section-based priority
    const section = change.section ? change.section.toUpperCase() : 'UNKNOWN';
    const basePriority = sectionPriorities[section] || 'Low';
    
    // Mark routine changes
    const isRoutine = routinePatterns.some(pattern => 
      change.item && change.item.toUpperCase().includes(pattern)
    );
    
    // Apply professional terminology
    const changeType = change.type || '';
    let description = '';
    
    if (changeType === 'added') {
      // Use "introduced" for new items
      description = `${change.employee || 'Employee'}: ${change.item} introduced in ${change.currentPeriod || 'current period'}: ${change.currentValue || ''}`;
    } else if (changeType === 'removed') {
      description = `${change.employee || 'Employee'}: ${change.item} removed from ${change.previousPeriod || 'previous period'}`;
    } else {
      // Use "changed" for modifications
      const difference = calculateDifference(change.previousValue, change.currentValue);
      const changeDirection = difference > 0 ? 'increase' : 'decrease';
      description = `${change.employee || 'Employee'}: ${change.item} changed from ${change.previousValue || ''} in ${change.previousPeriod || 'previous period'} to ${change.currentValue || ''} in ${change.currentPeriod || 'current period'}: ${changeDirection} ${Math.abs(difference).toFixed(2)}`;
    }
    
    return {
      ...change,
      priority: basePriority,
      routine: isRoutine,
      description: description,
      includeInReport: (basePriority === 'High' || basePriority === 'Medium') && !isRoutine
    };
  });
}

/**
 * Calculate the numeric difference between two values
 * @param {string|number} prevValue - Previous value
 * @param {string|number} currValue - Current value
 * @returns {number} - The calculated difference
 */
function calculateDifference(prevValue, currValue) {
  const prev = parseFloat(prevValue) || 0;
  const curr = parseFloat(currValue) || 0;
  return curr - prev;
}

// Helper function to calculate numeric differences for payroll values
function processPrioritizedChanges(changes) {
  // This function processes and categorizes changes by priority
  if (!changes || !Array.isArray(changes)) return changes;
  
  console.log(`[ENHANCED-AUDIT] Processing ${changes.length} changes with priority categorization`);
  
  // Priority section mapping
  const sectionPriorities = {
    'PERSONAL DETAILS': 'High',
    'EARNINGS': 'High',
    'DEDUCTIONS': 'High',
    'LOANS': 'Medium', // Moderate converted to Medium
    'EMPLOYERS CONTRIBUTION': 'Low',
    'EMPLOYEE BANK DETAILS': 'Medium'  // Moderate converted to Medium
  };
  
  // Process each change
  return changes.map(change => {
    // Determine priority based on section
    const section = change.section ? change.section.toUpperCase() : 'UNKNOWN';
    change.priority = sectionPriorities[section] || 'Low';
    
    // Handle routine changes filtering
    const routinePatterns = [
      'STAFF CREDIT UNION',
      'CREDIT UNION CONTRIBUTION',
      'STAFF SAVINGS',
      'VOLUNTARY DEDUCTION',
      'SEASONAL ADJUSTMENT'
    ];
    
    change.isRoutine = routinePatterns.some(pattern => 
      change.item && change.item.toUpperCase().includes(pattern)
    );
    
    // Use professional terminology for change descriptions
    if (change.type === 'added' || change.type === 'new') {
      change.terminology = 'introduced';
    } else {
      change.terminology = 'changed';
    }
    
    // Determine if this change should be included in the final report
    // High and Medium priority non-routine changes are included
    change.includeInReport = 
      (change.priority === 'High' || change.priority === 'Medium') && 
      !change.isRoutine;
      
    return change;
  });
}

// Load single source comparison data handler
ipcMain.handle('load-single-source-comparison-data', async () => {
  try {
    const fs = require('fs');
    const path = require('path');

    const singleSourceFile = path.join(__dirname, 'temp_report_data.json');

    if (!fs.existsSync(singleSourceFile)) {
      return {
        success: false,
        error: 'Single source comparison data file not found - run payroll audit first'
      };
    }

    const fileContent = fs.readFileSync(singleSourceFile, 'utf8');
    const reportData = JSON.parse(fileContent);

    console.log('Loaded single source data:', {
      format_version: reportData.format_version,
      timestamp: reportData.timestamp,
      comparison_results_count: reportData.comparisonData?.comparison_results?.length || 0
    });

    return {
      success: true,
      data: reportData
    };

  } catch (error) {
    console.error('Error loading single source comparison data:', error);
    return {
      success: false,
      error: `Failed to load comparison data: ${error.message}`
    };
  }
});

// ========== UNIFIED DATABASE IPC HANDLERS ==========

// Get comprehensive database statistics
ipcMain.handle('get-database-stats', async () => {
  try {
    if (!databaseManager || !databaseManager.isInitialized) {
      return { success: false, error: 'Database not initialized' };
    }

    const stats = await databaseManager.getComprehensiveStats();
    return { success: true, stats: stats };
  } catch (error) {
    console.error('[DATABASE] Error getting stats:', error);
    return { success: false, error: error.message };
  }
});

// Perform database maintenance
ipcMain.handle('perform-database-maintenance', async (event, daysOld = 30) => {
  try {
    if (!databaseManager || !databaseManager.isInitialized) {
      return { success: false, error: 'Database not initialized' };
    }

    const result = await databaseManager.performMaintenance(daysOld);
    return { success: true, result: result };
  } catch (error) {
    console.error('[DATABASE] Maintenance error:', error);
    return { success: false, error: error.message };
  }
});

// Clean extracted data - SAFE VERSION with better error handling
ipcMain.handle('clean-extracted-data', async (event, options = {}) => {
  try {
    if (!databaseManager || !databaseManager.isConnected) {
      return { success: false, error: 'Database not initialized' };
    }

    console.log('[DATABASE] Cleaning extracted payroll audit data with options:', options);

    // CRITICAL FIX: Add timeout protection to prevent FATAL ERRORS
    const cleanPromise = databaseManager.cleanExtractedData(options);
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Clean operation timed out')), 10000); // 10 second timeout
    });

    const result = await Promise.race([cleanPromise, timeoutPromise]);

    console.log('[DATABASE] Extracted payroll audit data cleaned successfully');

    // CRITICAL FIX: Add small delay to prevent callback race conditions
    await new Promise(resolve => setTimeout(resolve, 100));

    return { success: true, result: result };
  } catch (error) {
    console.error('[DATABASE] Clean extracted data error:', error);

    // CRITICAL FIX: Ensure we always return a safe response
    return {
      success: false,
      error: error.message || 'Unknown database error',
      timestamp: new Date().toISOString()
    };
  }
});

// Complete PRE_REPORTING phase after user interaction
ipcMain.handle('complete-pre-reporting-phase', async (event, selectedCount = 0) => {
  try {
    console.log(`[PRE-REPORTING] Completing phase with ${selectedCount} user selections`);

    const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');
    const result = await runHybridScript(pythonPath, ['complete-pre-reporting', selectedCount.toString()]);

    console.log('[PRE-REPORTING] Phase completion result:', result);
    return result;
  } catch (error) {
    console.error('[PRE-REPORTING] Phase completion error:', error);
    return { success: false, error: error.message };
  }
});

// Clean duplicate audit sessions
ipcMain.handle('clean-duplicate-sessions', async (event) => {
  try {
    if (!databaseManager || !databaseManager.isConnected) {
      return { success: false, error: 'Database not initialized' };
    }

    console.log('[DATABASE] Cleaning duplicate audit sessions...');
    const result = await databaseManager.cleanDuplicateAuditSessions();

    console.log('[DATABASE] Duplicate audit sessions cleaned successfully');
    return result;
  } catch (error) {
    console.error('[DATABASE] Clean duplicate sessions error:', error);
    return { success: false, error: error.message };
  }
});

// Complete Migration IPC Handlers
ipcMain.handle('migrate-storage-to-database', async (event) => {
  try {
    console.log('[COMPLETE-MIGRATION] Starting complete migration to database...');

    const migrationManager = new CompleteMigrationManager();
    await migrationManager.initialize();

    const result = await migrationManager.performCompleteMigration();
    await migrationManager.close();

    if (result.success) {
      console.log('[COMPLETE-MIGRATION] ✅ Complete migration successful');

      // Send progress updates to frontend
      safeSendToRenderer(event, 'migration-progress', {
        type: 'completed',
        message: 'Complete migration to database successful - All JSON dependencies removed',
        migrationLog: result.migrationLog,
        verification: result.verification
      });
    } else {
      console.error('[COMPLETE-MIGRATION] ❌ Complete migration failed:', result.error);
    }

    return result;
  } catch (error) {
    console.error('[MIGRATION] Migration error:', error);
    return { success: false, error: error.message };
  }
});

// Check if migration is needed
ipcMain.handle('check-migration-status', async () => {
  try {
    const fs = require('fs');
    const path = require('path');

    const oldFiles = [
      path.join(__dirname, 'data', 'payroll_dictionary.json'),
      path.join(__dirname, 'data', 'app_settings.json'),
      path.join(__dirname, 'data', 'reports')
    ];

    const existingFiles = oldFiles.filter(file => fs.existsSync(file));

    return {
      success: true,
      migrationNeeded: existingFiles.length > 0,
      existingFiles: existingFiles.map(file => path.basename(file)),
      recommendation: existingFiles.length > 0 ?
        'Migration recommended to improve performance' :
        'No migration needed - using unified database'
    };
  } catch (error) {
    console.error('[MIGRATION] Check status error:', error);
    return { success: false, error: error.message };
  }
});

// Data Builder IPC Handlers
ipcMain.handle('call-data-builder-api', async (event, command, args = []) => {
  try {
    console.log(`[DATA-BUILDER] API call: ${command} with args:`, args);

    const pythonPath = path.join(__dirname, 'data_builder_api.py');
    const pythonArgs = [command, ...args];

    const result = await runHybridScript(pythonPath, pythonArgs);

    try {
      // Clean the result to extract only valid JSON
      let cleanResult = result.trim();

      // Find the first { or [ to locate JSON start
      const jsonStart = cleanResult.search(/[{\[]/);
      if (jsonStart > 0) {
        cleanResult = cleanResult.substring(jsonStart);
      }

      // Find the last } or ] to locate JSON end
      const jsonEnd = cleanResult.lastIndexOf('}');
      if (jsonEnd !== -1 && jsonEnd < cleanResult.length - 1) {
        cleanResult = cleanResult.substring(0, jsonEnd + 1);
      }

      return JSON.parse(cleanResult);
    } catch (parseError) {
      console.error('[DATA-BUILDER] Failed to parse API response:', parseError);
      console.error('[DATA-BUILDER] Raw result:', result.substring(0, 200) + '...');
      return { success: false, error: 'Failed to parse API response' };
    }
  } catch (error) {
    console.error('[DATA-BUILDER] API call failed:', error);
    return { success: false, error: error.message };
  }
});

// REMOVED: Redundant compare-payrolls-with-fallback handler
// This was a duplicate of compare-payrolls with misleading name
// Use compare-payrolls instead (line 541)

ipcMain.handle('generate-report', async (event, reportData, options) => {
  try {
    console.log(`[PERFECT-REPORTS] STARTING 100% ACCURATE REPORT GENERATION:`);
    console.log(`[PERFECT-REPORTS]    Report formats: ${options.format}`);
    console.log(`[PERFECT-REPORTS]    Include signature: ${options.include_signature}`);
    console.log(`[PERFECT-REPORTS]    Report type: ${reportData.report_type || 'traditional'}`);

    // Use Perfect Section-Aware Extractor for report generation
    const pythonPath = path.join(__dirname, 'core', 'perfect_extraction_integration.py');

    // NO MORE TEMP FILES - Use database directly!
    const result = await runHybridScript(pythonPath, ['generate-report']);

    console.log(`[PERFECT-REPORTS] REPORT GENERATION COMPLETED with 100% accuracy`);
    return JSON.parse(result);
  } catch (error) {
    console.error(`[PERFECT-REPORTS] REPORT GENERATION FAILED: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Add missing downloadReport handler
ipcMain.handle('download-report', async (event, reportRequest) => {
  try {
    console.log(`[DOWNLOAD-REPORT] Starting report download: ${reportRequest.format}`);

    const result = await dialog.showSaveDialog(mainWindow, {
      filters: [
        { name: `${reportRequest.format.toUpperCase()} Files`, extensions: [reportRequest.format] }
      ],
      defaultPath: `payroll_audit_report.${reportRequest.format}`
    });

    if (result.filePath) {
      // Use the existing generate-report functionality to create the file
      const reportResult = await runHybridScript(
        path.join(__dirname, 'core', 'perfect_extraction_integration.py'),
        ['download-report', JSON.stringify(reportRequest), result.filePath]
      );

      console.log(`[DOWNLOAD-REPORT] Report downloaded to: ${result.filePath}`);
      return { success: true, filePath: result.filePath };
    }

    return { success: false, error: 'No file path selected' };
  } catch (error) {
    console.error(`[DOWNLOAD-REPORT] Download failed: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Save report to unified database (updated for database-first approach)
ipcMain.handle('save-to-report-manager', async (event, reportData) => {
  try {
    console.log(`[SAVE-REPORT-MANAGER] Saving report to unified database...`);

    // Use unified database instead of file-based storage
    if (databaseManager && databaseManager.isInitialized) {
      console.log('[SAVE-REPORT-MANAGER] Using unified database...');

      // Convert report data to database format
      const dbReport = {
        report_id: reportData.id || `report_${Date.now()}`,
        report_type: reportData.report_type || reportData.type || 'payroll_audit',
        report_category: determineReportCategory(reportData),
        title: reportData.title || `${reportData.report_type || 'Payroll'} Report`,
        description: reportData.description || `Generated on ${new Date().toLocaleDateString()}`,
        file_paths: reportData.report_paths || {},
        metadata: {
          timestamp: reportData.timestamp || new Date().toISOString(),
          source_tab: reportData.source_tab || 'payroll_audit',
          current_month: reportData.current_month,
          current_year: reportData.current_year,
          previous_month: reportData.previous_month,
          previous_year: reportData.previous_year,
          report_name: reportData.report_name,
          report_designation: reportData.report_designation,
          ...reportData.metadata
        },
        file_size: reportData.file_size || 0
      };

      await databaseManager.database.saveReport(dbReport);

      console.log(`[SAVE-REPORT-MANAGER] ✅ Report saved to database: ${dbReport.report_id}`);

      return {
        success: true,
        report_id: dbReport.report_id,
        storage_type: 'database',
        message: 'Report saved to unified database'
      };

    } else {
      // Initialize database if not ready and retry
      console.log('[SAVE-REPORT-MANAGER] Database not ready, initializing...');
      await initializeDatabaseSystem();

      if (databaseManager && databaseManager.isInitialized) {
        console.log('[SAVE-REPORT-MANAGER] Retrying save to database...');
        const result = await databaseManager.database.saveReport(reportData);
        console.log('[SAVE-REPORT-MANAGER] ✅ Report saved to database (retry)');
        return { success: true, reportId: result.reportId };
      } else {
        console.error('[SAVE-REPORT-MANAGER] ❌ Database initialization failed, cannot save report');
        return { success: false, error: 'Database not available' };
      }
    }
  } catch (error) {
    console.error(`[SAVE-REPORT-MANAGER] Save failed: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Helper function to determine report category
function determineReportCategory(reportData) {
  const sourceTab = reportData.source_tab || reportData.type || '';

  if (sourceTab.includes('payroll') || sourceTab.includes('audit')) {
    return 'Payroll_Audit_Reports';
  } else if (sourceTab.includes('pdf') || sourceTab.includes('sort')) {
    return 'PDF_Sorter_Reports';
  } else if (sourceTab.includes('data') || sourceTab.includes('builder')) {
    return 'Data_Builder_Reports';
  } else if (sourceTab.includes('bank') || sourceTab.includes('adviser')) {
    return 'Bank_Adviser_Reports';
  }

  return 'Payroll_Audit_Reports'; // Default category
}

// Dictionary management handlers
ipcMain.handle('get-enhanced-dictionary', async () => {
  try {
    // Use fast database manager
    if (databaseManager && databaseManager.isConnected) {
      console.log('[DICTIONARY] Loading from unified database...');
      const dictionary = await databaseManager.loadDictionary();
      console.log('[DICTIONARY] ✅ Dictionary loaded from database');
      return dictionary;
    } else {
      // Try to get database instance if not available
      console.log('[DICTIONARY] Getting database instance...');
      const db = await DatabaseManager.getInstance();
      const dictionary = await db.loadDictionary();
      console.log('[DICTIONARY] ✅ Dictionary loaded from database instance');
      return dictionary;
    }
  } catch (error) {
    console.error('[DICTIONARY] ❌ Error loading dictionary:', error);

    // Fallback to Python script if database fails
    try {
      console.log('[DICTIONARY] Fallback to Python script...');
      const pythonPath = path.join(__dirname, 'core', 'clean_payroll_dictionaries.py');
      const result = await runHybridScript(pythonPath, ['get-dictionary']);
      return JSON.parse(result);
    } catch (fallbackError) {
      console.error('[DICTIONARY] ❌ Fallback also failed:', fallbackError);
      return null;
    }
  }
});

// Add missing getDictionaryStats handler (returns actual stats, not dictionary data)
ipcMain.handle('get-dictionary-stats', async () => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'clean_payroll_dictionaries.py');
    const result = await runHybridScript(pythonPath, ['get-stats']);
    return JSON.parse(result);
  } catch (error) {
    console.error('Error getting dictionary stats:', error);
    return { total_items: 0, total_sections: 0 };
  }
});

ipcMain.handle('save-enhanced-dictionary', async (event, dictionary) => {
  try {
    // Use fast database manager
    if (databaseManager && databaseManager.isConnected) {
      console.log('[DICTIONARY] Saving to unified database...');
      await databaseManager.saveDictionary(dictionary);
      console.log('[DICTIONARY] ✅ Dictionary saved to database');
      return true;
    } else {
      // Try to get database instance if not available
      console.log('[DICTIONARY] Getting database instance for save...');
      const db = await DatabaseManager.getInstance();
      await db.saveDictionary(dictionary);
      console.log('[DICTIONARY] ✅ Dictionary saved to database instance');
      return true;
    }
  } catch (error) {
    console.error('[DICTIONARY] ❌ Error saving dictionary:', error);

    // Fallback to Python script if database fails
    try {
      console.log('[DICTIONARY] Fallback to Python script for save...');
      const pythonPath = path.join(__dirname, 'core', 'clean_payroll_dictionaries.py');
      const result = await runHybridScript(pythonPath, ['save-dictionary', JSON.stringify(dictionary)]);
      return result.trim() === 'true';
    } catch (fallbackError) {
      console.error('[DICTIONARY] ❌ Fallback save also failed:', fallbackError);
      return false;
    }
  }
});

ipcMain.handle('reset-enhanced-dictionary', async () => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'clean_payroll_dictionaries.py');
    const result = await runHybridScript(pythonPath, ['clear-session']);
    return result.trim() === 'true';
  } catch (error) {
    console.error('Error resetting dictionary:', error);
    return false;
  }
});

ipcMain.handle('import-dictionary', async () => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openFile'],
      filters: [
        { name: 'JSON Files', extensions: ['json'] }
      ]
    });

    if (result.filePaths.length > 0) {
      const pythonPath = path.join(__dirname, 'core', 'clean_payroll_dictionaries.py');
      const importResult = await runHybridScript(pythonPath, ['import', result.filePaths[0]]);
      return { success: importResult.trim() === 'true' };
    }

    return { success: false, error: 'No file selected' };
  } catch (error) {
    console.error('Error importing dictionary:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('export-dictionary', async () => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      filters: [
        { name: 'JSON Files', extensions: ['json'] }
      ],
      defaultPath: 'payroll_dictionary_backup.json'
    });

    if (result.filePath) {
      const pythonPath = path.join(__dirname, 'core', 'clean_payroll_dictionaries.py');
      const exportResult = await runHybridScript(pythonPath, ['export']);

      // The export command returns the path, copy it to the selected location
      if (exportResult && exportResult.trim() !== 'false') {
        const fs = require('fs');
        fs.copyFileSync(exportResult.trim(), result.filePath);
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Error exporting dictionary:', error);
    return false;
  }
});

// ========== PRE-REPORTING DATA HANDLERS ==========

// Get current session ID
ipcMain.handle('get-current-session-id', async (event) => {
  try {
    console.log('[SESSION] Getting current session ID...');

    const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');
    const result = await runHybridScript(pythonPath, ['get-current-session-id']);
    return JSON.parse(result);
  } catch (error) {
    console.error('[SESSION] Error getting current session ID:', error);
    return { success: false, error: error.message };
  }
});

// Get pre-reporting data by session ID
ipcMain.handle('get-pre-reporting-data', async (event, sessionId) => {
  try {
    console.log(`[PRE-REPORTING] Getting pre-reporting data for session: ${sessionId}`);

    const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');
    const result = await runHybridScript(pythonPath, ['get-pre-reporting-data', sessionId]);

    // CRITICAL FIX: Better result parsing and validation
    console.log('[PRE-REPORTING] Raw result type:', typeof result);
    console.log('[PRE-REPORTING] Raw result length:', result?.length || 0);

    if (!result || result.trim() === '') {
      console.warn(`[PRE-REPORTING] Empty result for session ${sessionId}`);
      return { success: false, error: 'No data returned from backend' };
    }

    try {
      const parsedResult = JSON.parse(result);
      console.log('[PRE-REPORTING] Parsed result success:', parsedResult?.success);
      console.log('[PRE-REPORTING] Parsed result data length:', parsedResult?.data?.length || 0);
      return parsedResult;
    } catch (parseError) {
      console.error('[PRE-REPORTING] JSON parse error:', parseError);
      console.error('[PRE-REPORTING] Raw result that failed to parse:', result);
      return { success: false, error: 'Failed to parse backend response' };
    }
  } catch (error) {
    console.error('[PRE-REPORTING] Error getting pre-reporting data:', error);
    return { success: false, error: error.message };
  }
});

// Get latest pre-reporting data
ipcMain.handle('get-latest-pre-reporting-data', async () => {
  try {
    console.log('[PRE-REPORTING] Getting latest pre-reporting data...');

    const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');
    const result = await runHybridScript(pythonPath, ['get-latest-pre-reporting-data']);

    // CRITICAL FIX: Better result parsing and validation
    console.log('[PRE-REPORTING] Raw result type:', typeof result);
    console.log('[PRE-REPORTING] Raw result length:', result?.length || 0);

    if (!result || result.trim() === '') {
      console.warn('[PRE-REPORTING] Empty result from Python script');
      return { success: false, error: 'No data returned from backend' };
    }

    try {
      const parsedResult = JSON.parse(result);
      console.log('[PRE-REPORTING] Parsed result success:', parsedResult?.success);
      console.log('[PRE-REPORTING] Parsed result data length:', parsedResult?.data?.length || 0);
      return parsedResult;
    } catch (parseError) {
      console.error('[PRE-REPORTING] JSON parse error:', parseError);
      console.error('[PRE-REPORTING] Raw result that failed to parse:', result);
      return { success: false, error: 'Failed to parse backend response' };
    }
  } catch (error) {
    console.error('[PRE-REPORTING] Error getting latest pre-reporting data:', error);
    return { success: false, error: error.message };
  }
});

// 🎯 BULLETPROOF: Populate tracker tables from results
ipcMain.handle('populate-tracker-tables', async () => {
  try {
    console.log('[TRACKER-POPULATION] Populating Bank Adviser tables from tracker results...');

    const pythonPath = path.join(__dirname, 'bank_adviser_tracker_operations.py');
    const result = await runHybridScript(pythonPath, ['populate_tables']);
    return JSON.parse(result);
  } catch (error) {
    console.error('[TRACKER-POPULATION] Error populating tracker tables:', error);
    return { success: false, error: error.message };
  }
});

// Update pre-reporting selections
ipcMain.handle('update-pre-reporting-selections', async (event, selections) => {
  try {
    console.log('[PRE-REPORTING] Updating selections:', selections);

    const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');
    const result = await runHybridScript(pythonPath, ['update-pre-reporting-selections', JSON.stringify(selections)]);
    return JSON.parse(result);
  } catch (error) {
    console.error('[PRE-REPORTING] Error updating selections:', error);
    return { success: false, error: error.message };
  }
});

// Generate final reports after user approval
ipcMain.handle('generate-final-reports', async (event, reportData) => {
  try {
    // Handle both old format (sessionId only) and new format (reportData object)
    let sessionId, selectedChanges, reportOptions;

    if (typeof reportData === 'string') {
      // Old format - just session ID
      sessionId = reportData;
      selectedChanges = [];
      reportOptions = {};
    } else if (typeof reportData === 'object' && reportData !== null) {
      // New format - report data object
      sessionId = reportData.sessionId;
      selectedChanges = reportData.selectedChanges || [];
      reportOptions = reportData.reportOptions || {};
    } else {
      sessionId = reportData;
      selectedChanges = [];
      reportOptions = {};
    }

    console.log('[REPORT-GENERATION] Generating final reports for session:', sessionId);
    console.log('[REPORT-GENERATION] Selected changes count:', selectedChanges.length);
    console.log('[REPORT-GENERATION] Report options:', reportOptions);

    const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');

    // Pass the complete report data as JSON to the Python script
    const reportDataJson = JSON.stringify({
      sessionId: sessionId,
      selectedChanges: selectedChanges,
      reportOptions: reportOptions
    });

    const result = await runHybridScript(pythonPath, ['generate-final-reports', reportDataJson]);

    // CRITICAL FIX: Better result parsing and validation
    console.log('[REPORT-GENERATION] Raw result type:', typeof result);
    console.log('[REPORT-GENERATION] Raw result length:', result?.length || 0);

    if (!result || result.trim() === '') {
      console.warn('[REPORT-GENERATION] Empty result from Python script');
      return { success: false, error: 'No response from report generation backend' };
    }

    try {
      const parsedResult = JSON.parse(result);
      console.log('[REPORT-GENERATION] Parsed result success:', parsedResult?.success);
      return parsedResult;
    } catch (parseError) {
      console.error('[REPORT-GENERATION] JSON parse error:', parseError);
      console.error('[REPORT-GENERATION] Raw result that failed to parse:', result);
      return { success: false, error: 'Failed to parse report generation response' };
    }
  } catch (error) {
    console.error('[REPORT-GENERATION] Error generating reports:', error);
    return { success: false, error: error.message };
  }
});

// Process control APIs
ipcMain.handle('pause-process', async (event) => {
  try {
    console.log('[PROCESS-CONTROL] Pausing process...');
    const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');
    const result = await runHybridScript(pythonPath, ['pause-process']);
    return JSON.parse(result);
  } catch (error) {
    console.error('[PROCESS-CONTROL] Error pausing process:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('resume-process', async (event) => {
  try {
    console.log('[PROCESS-CONTROL] Resuming process...');
    const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');
    const result = await runHybridScript(pythonPath, ['resume-process']);
    return JSON.parse(result);
  } catch (error) {
    console.error('[PROCESS-CONTROL] Error resuming process:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('stop-process', async (event) => {
  try {
    console.log('[PROCESS-CONTROL] Stopping process...');
    const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');
    const result = await runHybridScript(pythonPath, ['stop-process']);
    return JSON.parse(result);
  } catch (error) {
    console.error('[PROCESS-CONTROL] Error stopping process:', error);
    return { success: false, error: error.message };
  }
});

// PRODUCTION GUARD: Phase data verification handler
ipcMain.handle('verify-phase-data', async (event, phaseName) => {
  try {
    console.log(`[GUARD] Verifying phase data for: ${phaseName}`);

    const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');
    const result = await runHybridScript(pythonPath, ['verify-phase-data', phaseName]);

    // Parse result - should be JSON with hasData boolean
    const parsed = JSON.parse(result);
    const hasData = parsed.hasData || false;
    const recordCount = parsed.recordCount || 0;

    console.log(`[GUARD] Phase ${phaseName} verification: ${recordCount} records found, hasData: ${hasData}`);

    return hasData;

  } catch (error) {
    console.error(`[GUARD] Error verifying phase data for ${phaseName}:`, error);
    return false;
  }
});

// ========== ENHANCED LOAN MANAGEMENT HANDLERS ==========

// Create a new loan type
ipcMain.handle('create-loan-type', async (event, loanTypeName, classification) => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'dictionary_manager.py');
    const result = await runHybridScript(pythonPath, ['create-loan-type', loanTypeName, classification]);
    return result.trim() === 'true';
  } catch (error) {
    console.error('Error creating loan type:', error);
    return false;
  }
});

// Update loan type classification
ipcMain.handle('update-loan-type-classification', async (event, loanTypeName, classification) => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'dictionary_manager.py');
    const result = await runHybridScript(pythonPath, ['update-loan-classification', loanTypeName, classification]);
    return result.trim() === 'true';
  } catch (error) {
    console.error('Error updating loan type classification:', error);
    return false;
  }
});

// Delete a loan type
ipcMain.handle('delete-loan-type', async (event, loanTypeName) => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'dictionary_manager.py');
    const result = await runHybridScript(pythonPath, ['delete-loan-type', loanTypeName]);
    return result.trim() === 'true';
  } catch (error) {
    console.error('Error deleting loan type:', error);
    return false;
  }
});

// Get all loan types
ipcMain.handle('get-loan-types', async () => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'dictionary_manager.py');
    const result = await runHybridScript(pythonPath, ['get-loan-types']);

    // Clean the result to extract only valid JSON
    let cleanResult = result.trim();

    // Find the first { to locate JSON start
    const jsonStart = cleanResult.search(/[{]/);
    if (jsonStart > 0) {
      cleanResult = cleanResult.substring(jsonStart);
    }

    // Find the last } to locate JSON end
    const jsonEnd = cleanResult.lastIndexOf('}');
    if (jsonEnd !== -1 && jsonEnd < cleanResult.length - 1) {
      cleanResult = cleanResult.substring(0, jsonEnd + 1);
    }

    return JSON.parse(cleanResult);
  } catch (error) {
    console.error('Error getting loan types:', error);
    console.error('Raw result:', result?.substring(0, 200) + '...');
    return {};
  }
});

// Get loan classification summary
ipcMain.handle('get-loan-classification-summary', async () => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'dictionary_manager.py');
    const result = await runHybridScript(pythonPath, ['get-classification-summary']);
    return JSON.parse(result);
  } catch (error) {
    console.error('Error getting classification summary:', error);
    return {};
  }
});

// Auto-group loan items
ipcMain.handle('auto-group-loan-items', async () => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'dictionary_manager.py');
    const result = await runHybridScript(pythonPath, ['auto-group-loans']);
    return JSON.parse(result);
  } catch (error) {
    console.error('Error auto-grouping loan items:', error);
    return { error: error.message };
  }
});

// Add item to loan type
ipcMain.handle('add-item-to-loan-type', async (event, loanTypeName, itemName, columnType) => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'dictionary_manager.py');
    const result = await runHybridScript(pythonPath, ['add-item-to-loan-type', loanTypeName, itemName, columnType]);
    return result.trim() === 'true';
  } catch (error) {
    console.error('Error adding item to loan type:', error);
    return false;
  }
});

// Remove item from loan type
ipcMain.handle('remove-item-from-loan-type', async (event, loanTypeName, itemName) => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'dictionary_manager.py');
    const result = await runHybridScript(pythonPath, ['remove-item-from-loan-type', loanTypeName, itemName]);
    return result.trim() === 'true';
  } catch (error) {
    console.error('Error removing item from loan type:', error);
    return false;
  }
});

// Detect ungrouped loan items
ipcMain.handle('detect-ungrouped-loan-items', async () => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'dictionary_manager.py');
    const result = await runHybridScript(pythonPath, ['detect-ungrouped-items']);
    return JSON.parse(result);
  } catch (error) {
    console.error('Error detecting ungrouped loan items:', error);
    return [];
  }
});

// Export loan classification report
ipcMain.handle('export-loan-classification-report', async (event, format) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      filters: [
        { name: 'Excel Files', extensions: ['xlsx'] },
        { name: 'JSON Files', extensions: ['json'] },
        { name: 'CSV Files', extensions: ['csv'] }
      ],
      defaultPath: `loan_classification_report.${format}`
    });

    if (result.filePath) {
      const pythonPath = path.join(__dirname, 'core', 'dictionary_manager.py');
      const exportResult = await runHybridScript(pythonPath, ['export-classification-report', format, result.filePath]);
      return exportResult.trim() === 'true';
    }

    return false;
  } catch (error) {
    console.error('Error exporting classification report:', error);
    return false;
  }
});

// NEW Auto-Learning API handlers - using phased process manager
ipcMain.handle('get-pending-items', async () => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');
    const result = await runHybridScript(pythonPath, ['get-pending-items']);

    // Clean the result to extract only valid JSON
    let cleanResult = result.trim();

    // Find the first { to locate JSON start
    const jsonStart = cleanResult.search(/[{]/);
    if (jsonStart > 0) {
      cleanResult = cleanResult.substring(jsonStart);
    }

    // Find the last } to locate JSON end
    const jsonEnd = cleanResult.lastIndexOf('}');
    if (jsonEnd !== -1 && jsonEnd < cleanResult.length - 1) {
      cleanResult = cleanResult.substring(0, jsonEnd + 1);
    }

    return JSON.parse(cleanResult);
  } catch (error) {
    console.error('Error getting pending items:', error);
    console.error('Raw result:', result?.substring(0, 200) + '...');
    return { success: false, error: error.message, data: [] };
  }
});

ipcMain.handle('approve-pending-item', async (event, itemId, standardizedName, targetSection) => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');
    const args = ['approve-pending-item', itemId];
    if (standardizedName) args.push('--standardized-name', standardizedName);
    if (targetSection) args.push('--target-section', targetSection);

    const result = await runHybridScript(pythonPath, args);
    return JSON.parse(result);
  } catch (error) {
    console.error('Error approving pending item:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('reject-pending-item', async (event, itemId, reason) => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');
    const args = ['reject-pending-item', itemId];
    if (reason) args.push(reason);

    const result = await runHybridScript(pythonPath, args);
    return JSON.parse(result);
  } catch (error) {
    console.error('Error rejecting pending item:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-auto-learning-stats', async () => {
  try {
    const pythonPath = path.join(__dirname, 'core', 'phased_process_manager.py');
    const result = await runHybridScript(pythonPath, ['get-auto-learning-stats']);
    return JSON.parse(result);
  } catch (error) {
    console.error('Error getting auto-learning stats:', error);
    return { success: false, error: error.message };
  }
});

// Removed old auto-learning session handlers - now integrated into phased process manager

// Removed duplicate reject-pending-item handler - using enhanced version above

// Removed bulk approve/reject handlers - can be implemented later if needed

// Reset Auto-Learning data
ipcMain.handle('reset-auto-learning', async () => {
  try {
    console.log('[RESET] Resetting Auto-Learning data...');
    const pythonPath = path.join(__dirname, 'core', 'clean_payroll_dictionaries.py');
    const result = await runHybridScript(pythonPath, ['clear-session']);

    if (result.trim() === 'true') {
      console.log('[RESET] Auto-Learning data reset successfully');
      return { success: true };
    } else {
      console.error('[RESET] Failed to reset Auto-Learning data');
      return { success: false, error: 'Failed to reset Auto-Learning data' };
    }
  } catch (error) {
    console.error('[RESET] Error resetting Auto-Learning:', error);
    return { success: false, error: error.message };
  }
});

// Clean All App Data
ipcMain.handle('clean-all-app-data', async () => {
  try {
    console.log('[CLEAN] Starting clean all data operation...');

    // 1. Clear Auto-Learning data
    const pythonPath = path.join(__dirname, 'core', 'clean_payroll_dictionaries.py');
    await runHybridScript(pythonPath, ['clear-session']);

    // 2. Delete all reports
    const reportsDir = path.join(__dirname, 'data', 'reports');
    if (fs.existsSync(reportsDir)) {
      fs.rmSync(reportsDir, { recursive: true, force: true });
      console.log('[CLEAN] Reports directory deleted');
    }

    // 3. Delete Data Builder reports
    const dataBuilderReports = path.join(__dirname, 'data', 'reports', 'Data_Builder_Reports');
    if (fs.existsSync(dataBuilderReports)) {
      fs.rmSync(dataBuilderReports, { recursive: true, force: true });
      console.log('[CLEAN] Data Builder reports deleted');
    }

    // 4. Delete PDF Sorter reports
    const pdfSorterReports = path.join(__dirname, 'data', 'reports', 'PDF_Sorter_Reports');
    if (fs.existsSync(pdfSorterReports)) {
      fs.rmSync(pdfSorterReports, { recursive: true, force: true });
      console.log('[CLEAN] PDF Sorter reports deleted');
    }

    // 5. Delete Payroll Audit reports
    const payrollAuditReports = path.join(__dirname, 'data', 'reports', 'Payroll_Audit_Reports');
    if (fs.existsSync(payrollAuditReports)) {
      fs.rmSync(payrollAuditReports, { recursive: true, force: true });
      console.log('[CLEAN] Payroll Audit reports deleted');
    }

    // 6. Reset settings to defaults in database
    try {
      const defaultSettings = getDefaultSettings();
      await saveAppSettings(defaultSettings);
      console.log('[CLEAN] Settings reset to defaults in database');
    } catch (error) {
      console.error('[CLEAN] Error resetting settings in database:', error);
    }

    // 7. Clear any cached data files
    const cacheFiles = [
      path.join(__dirname, 'data_utilization_analysis.json'),
      path.join(__dirname, 'spreadsheet_analysis.json'),
      path.join(__dirname, 'comprehensive_data_analysis.json')
    ];

    for (const cacheFile of cacheFiles) {
      if (fs.existsSync(cacheFile)) {
        fs.unlinkSync(cacheFile);
        console.log(`[CLEAN] Cache file deleted: ${path.basename(cacheFile)}`);
      }
    }

    // 8. Recreate essential directories
    const essentialDirs = [
      path.join(__dirname, 'data'),
      path.join(__dirname, 'data', 'reports'),
      path.join(__dirname, 'data', 'reports', 'Payroll_Audit_Reports'),
      path.join(__dirname, 'data', 'reports', 'PDF_Sorter_Reports'),
      path.join(__dirname, 'data', 'reports', 'Data_Builder_Reports')
    ];

    for (const dir of essentialDirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`[CLEAN] Directory recreated: ${path.basename(dir)}`);
      }
    }

    console.log('[CLEAN] All data cleaned successfully');
    return { success: true };

  } catch (error) {
    console.error('[CLEAN] Error cleaning all data:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('update-item-section', async (event, itemId, newSection) => {
  try {
    // For now, just approve the item in the new section
    const pythonPath = path.join(__dirname, 'core', 'clean_payroll_dictionaries.py');
    const result = await runHybridScript(pythonPath, ['approve-item', itemId, newSection]);
    return result.trim() === 'true';
  } catch (error) {
    console.error('Error updating item section:', error);
    return false;
  }
});

ipcMain.handle('download-dictionary-template', async () => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      filters: [{ name: 'JSON Files', extensions: ['json'] }],
      defaultPath: 'dictionary_template.json'
    });

    if (result.filePath) {
      const pythonPath = path.join(__dirname, 'core', 'clean_payroll_dictionaries.py');
      const templateResult = await runHybridScript(pythonPath, ['export']);

      if (templateResult && templateResult.trim() !== 'false') {
        const fs = require('fs');
        fs.copyFileSync(templateResult.trim(), result.filePath);
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Error downloading template:', error);
    return false;
  }
});

// Clean function to run hybrid extractor scripts ONLY
function runHybridScript(scriptPath, args = []) {
  return new Promise((resolve, reject) => {
    console.log(`[HYBRID] Starting 100% accurate hybrid process: python ${scriptPath} ${args.join(' ')}`);

    const python = spawn('python', [scriptPath, ...args], {
      env: {
        ...process.env,
        PYTHONIOENCODING: 'utf-8',
        PYTHONUNBUFFERED: '1',  // Force unbuffered output
        PYTHONUTF8: '1',  // Force UTF-8 mode
        LANG: 'en_US.UTF-8',
        LC_ALL: 'en_US.UTF-8'
      },
      stdio: ['pipe', 'pipe', 'pipe'],
      encoding: 'utf8'
    });

    // Track process for stop functionality
    const processId = Date.now();
    currentProcesses.set(processId, python);

    let output = '';
    let error = '';

    python.stdout.on('data', (data) => {
      // Enhanced character encoding handling to prevent corrupted output
      const text = data.toString('utf8')
        .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, '') // Remove control characters
        .replace(/Γ£à/g, '✅') // Replace corrupted success symbols
        .replace(/≡ƒ[öäôèÅªöùôè]/g, '🔄') // Replace corrupted processing symbols
        .replace(/ΓÅ│/g, '⏳') // Replace corrupted waiting symbols
        .replace(/≡ƒöä/g, '🔄') // Starting symbols
        .replace(/≡ƒÅª/g, '🔄') // Manager symbols
        .replace(/≡ƒöù/g, '🔄') // Connection symbols
        .replace(/≡ƒôè/g, '📊'); // Data symbols
      output += text;

      // Log hybrid extractor output (only non-empty lines)
      const lines = text.split('\n').filter(line => line.trim() && !line.includes('Database migrations'));
      lines.forEach(line => {
        if (line.trim()) {
          console.log(`[HYBRID-BACKEND] ${line.trim()}`);
        }
      });
    });

    python.stderr.on('data', (data) => {
      // Enhanced error message encoding handling
      const text = data.toString('utf8')
        .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, '') // Remove control characters
        .replace(/Γ£à/g, '✅') // Replace corrupted success symbols
        .replace(/≡ƒ[öäôèÅªöùôè]/g, '🔄') // Replace corrupted processing symbols
        .replace(/ΓÅ│/g, '⏳') // Replace corrupted waiting symbols
        .replace(/≡ƒöä/g, '🔄') // Starting symbols
        .replace(/≡ƒÅª/g, '🔄') // Manager symbols
        .replace(/≡ƒöù/g, '🔄') // Connection symbols
        .replace(/≡ƒôè/g, '📊'); // Data symbols
      error += text;

      // Show errors immediately (filter out migration messages)
      if (text.trim() && !text.includes('Database migrations')) {
        console.error(`[HYBRID-ERROR] ${text.trim()}`);
      }
    });

    python.on('close', (code) => {
      console.log(`[HYBRID] Process completed with code: ${code}`);
      currentProcesses.delete(processId);

      if (code === 0) {
        resolve(output);
      } else {
        // CRITICAL FIX: Handle null exit codes properly
        const exitCode = code !== null ? code : 'null';
        const errorMessage = error || `Process exited with code ${exitCode}`;
        
        // Don't treat null exit codes as fatal errors if there's output
        if (code === null && finalOutput.trim()) {
          console.warn(`[PYTHON-REALTIME] Process ${processId} exited with null code but has output, treating as success`);
          resolve(finalOutput);
        } else {
          reject(new Error(errorMessage));
        }
      }
    });

    python.on('error', (err) => {
      console.error(`[HYBRID] Process error: ${err.message}`);
      currentProcesses.delete(processId);
      reject(err);
    });
  });
}

/**
 * Enhanced Python script runner with real-time progress updates
 */
async function runEnhancedPythonScript(scriptPath, args, event, options = {}) {
  return new Promise((resolve, reject) => {
    const timeout = options.timeout || 30 * 60 * 1000; // 30 minutes default

    console.log(`[ENHANCED-PYTHON] Running: python ${scriptPath} ${args.join(' ')}`);

    const pythonProcess = spawn('python', [scriptPath, ...args], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: __dirname,
      env: {
        ...process.env,
        PYTHONIOENCODING: 'utf-8',
        PYTHONUNBUFFERED: '1',
        PYTHONUTF8: '1',
        PYTHONLEGACYWINDOWSSTDIO: '0'
      }
    });

    // Track process for stop/pause functionality
    const processId = Date.now();
    currentProcesses.set(processId, pythonProcess);
    console.log(`[ENHANCED-PYTHON] Tracking process ${processId} for stop/pause functionality`);

    let outputData = '';
    let errorData = '';
    let processTimeout;

    // Set timeout
    if (timeout > 0) {
      processTimeout = setTimeout(() => {
        pythonProcess.kill('SIGTERM');
        reject(new Error(`Process timeout after ${timeout}ms`));
      }, timeout);
    }

    // Handle stdout (progress updates and final result)
    pythonProcess.stdout.on('data', (data) => {
      const output = data.toString();
      outputData += output;

      // Send real-time progress updates to renderer
      const lines = output.split('\n');
      lines.forEach(line => {
        if (line.trim()) {
          // Parse REALTIME_UPDATE JSON messages from Python
          if (line.includes('REALTIME_UPDATE:')) {
            try {
              // IMPROVED: More robust JSON extraction
              const updateJson = line.split('REALTIME_UPDATE:')[1].trim();

              // Handle potential JSON corruption by finding complete JSON objects
              let cleanJson = updateJson;

              // If JSON doesn't start with {, find the first {
              const jsonStart = updateJson.indexOf('{');
              if (jsonStart > 0) {
                cleanJson = updateJson.substring(jsonStart);
              }

              // If JSON has extra content after }, find the last }
              const jsonEnd = cleanJson.lastIndexOf('}');
              if (jsonEnd !== -1 && jsonEnd < cleanJson.length - 1) {
                cleanJson = cleanJson.substring(0, jsonEnd + 1);
              }

              let updateData = JSON.parse(cleanJson);

              // CRITICAL: Handle pre-reporting data ready events
              if (updateData.type === 'pre_reporting_data_ready') {
                console.log(`[PRE-REPORTING] Data ready with ${updateData.total_changes} changes`);
                // Send pre-reporting data to UI with error handling
                try {
                  if (event && event.sender && !event.sender.isDestroyed()) {
                    event.sender.send('pre-reporting-data-ready', updateData);
                  }
                } catch (senderError) {
                  console.log(`[PRE-REPORTING] Sender error (renderer likely closed): ${senderError.message}`);
                }
              }

              // Send ALL real-time updates to enhanced-progress-update channel with error handling
              try {
                if (event && event.sender && !event.sender.isDestroyed()) {
                  event.sender.send('enhanced-progress-update', updateData);
                  console.log(`[ENHANCED-PROGRESS] ${updateData.type}: ${updateData.message || 'Update received'}`);
                } else {
                  console.log(`[ENHANCED-PROGRESS] Skipping update - renderer destroyed: ${updateData.type}`);
                }
              } catch (senderError) {
                console.log(`[ENHANCED-PROGRESS] Sender error (renderer likely closed): ${senderError.message}`);
              }

              // Also send to legacy channel for backward compatibility
              if (updateData.type === 'phase_progress') {
                console.log(`[PHASE-PROGRESS] ${updateData.phase}: ${updateData.percentage}% - ${updateData.message}`);
              }
            } catch (parseError) {
              console.error('[ENHANCED-IPC] Error parsing REALTIME_UPDATE:', parseError);
              console.error('[ENHANCED-IPC] Problematic line:', line);
              // Continue processing instead of failing completely
            }
            return;
          }

          // Send general log updates with error handling
          try {
            if (event && event.sender && !event.sender.isDestroyed()) {
              event.sender.send('enhanced-log-update', {
                message: line.trim(),
                timestamp: new Date().toISOString(),
                type: 'info'
              });
            }
          } catch (senderError) {
            console.log(`[ENHANCED-LOG] Sender error (renderer likely closed): ${senderError.message}`);
          }
        }
      });
    });

    // Handle stderr
    pythonProcess.stderr.on('data', (data) => {
      const error = data.toString();
      errorData += error;

      // Send error updates to renderer (only real errors) with error handling
      try {
        if (event && event.sender && !event.sender.isDestroyed()) {
          event.sender.send('enhanced-log-update', {
            message: error.trim(),
            timestamp: new Date().toISOString(),
            type: 'error'
          });
        }
      } catch (senderError) {
        console.log(`[ENHANCED-ERROR] Sender error (renderer likely closed): ${senderError.message}`);
      }
    });

    // Handle process completion
    pythonProcess.on('close', (code) => {
      if (processTimeout) {
        clearTimeout(processTimeout);
      }

      // Clean up process tracking
      currentProcesses.delete(processId);
      console.log(`[ENHANCED-PYTHON] Process ${processId} exited with code: ${code}`);
      console.log(`[ENHANCED-PYTHON] Removed process ${processId} from tracking`);

      console.log(`[ENHANCED-PYTHON] Process exited with code: ${code}`);

      if (code === 0) {
        try {
          // Parse the JSON string into an object before resolving
          try {
            const parsedOutput = JSON.parse(outputData.trim());
            resolve(parsedOutput);
          } catch (parseError) {
            console.error(`[PYTHON-REALTIME] Failed to parse JSON: ${parseError.message}`);

            // PRODUCTION FIX: Filter out debug messages before parsing
            let filteredOutput = outputData
              .split('\n')
              .filter(line => !line.includes('[DEBUG]') && !line.includes('Raw options JSON'))
              .join('\n');

            console.error(`[PYTHON-REALTIME] Filtered JSON content (${filteredOutput.length} chars total)`);
            // Try to clean up the JSON before giving up
            try {
              // Find the first { or [ character to find where JSON starts
              let cleanJsonString = outputData.trim();
              const jsonStartIndex = cleanJsonString.search(/[{\[]/);
              if (jsonStartIndex !== -1) {
                cleanJsonString = cleanJsonString.substring(jsonStartIndex);
              }
              
              // Find the last } or ] character to find where JSON ends
              const jsonEndIndex = cleanJsonString.lastIndexOf('}');
              if (jsonEndIndex !== -1) {
                cleanJsonString = cleanJsonString.substring(0, jsonEndIndex + 1);
              }
              
              const cleanedParsedOutput = JSON.parse(cleanJsonString);
              console.log(`[PYTHON-REALTIME] Successfully parsed cleaned JSON`);
              resolve(cleanedParsedOutput);
            } catch (cleanupError) {
              // If cleanup attempt also fails, reject with original error
              reject(new Error(`Failed to parse JSON output: ${parseError.message}`));
            }
          }

          if (result) {
            resolve({ result, processId });
          } else {
            // Fallback: return raw output
            resolve({
              result: {
                success: true,
                output: outputData,
                process_type: 'ENHANCED_PROCESS'
              },
              processId
            });
          }

        } catch (error) {
          reject(new Error(`Failed to parse result: ${error.message}`));
        }
      } else {
        reject(new Error(`Process failed with code ${code}: ${errorData}`));
      }
    });

    // Handle process errors
    pythonProcess.on('error', (error) => {
      if (processTimeout) {
        clearTimeout(processTimeout);
      }

      // Clean up process tracking
      currentProcesses.delete(processId);
      console.log(`[ENHANCED-PYTHON] Removed process ${processId} from tracking due to error`);

      console.error('[ENHANCED-PYTHON] Process error:', error);
      reject(error);
    });
  });
}

// ============================================================================
// WORKER THREAD SYSTEM - NON-BLOCKING PYTHON PROCESSING
// ============================================================================

/**
 * Run Python script in worker thread (non-blocking alternative)
 * This function provides the same interface as runPythonScriptWithRealTimeUpdates
 * but executes in a worker thread to keep the main process responsive
 */
function runPythonScriptInWorker(scriptPath, args = [], event, options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`[WORKER-MAIN] Starting Python worker: ${scriptPath} ${args.join(' ')}`);

    const workerId = Date.now();

    // Create worker with progress and real-time update handlers
    const workerPromise = createPythonWorker(scriptPath, args, {
      timeout: options.timeout || 30 * 60 * 1000, // 30 minutes default

      // Handle progress updates from worker
      onProgress: (progressData) => {
        console.log(`[WORKER-MAIN] Progress from worker ${workerId}:`, progressData);
      },

      // Handle real-time updates from worker
      onRealtimeUpdate: (updateData) => {
        // Forward real-time updates to frontend (same as original system)
        safeSendToRenderer(event, 'realtime-extraction-update', updateData);
        console.log(`[WORKER-MAIN] Real-time update from worker ${workerId}:`, updateData.type);
      }
    });

    // Track active worker
    activeWorkers.set(workerId, { promise: workerPromise, startTime: Date.now() });

    // Handle worker completion
    workerPromise
      .then((result) => {
        console.log(`[WORKER-MAIN] Worker ${workerId} completed successfully`);
        activeWorkers.delete(workerId);
        resolve(result);
      })
      .catch((error) => {
        console.error(`[WORKER-MAIN] Worker ${workerId} failed:`, error.message);
        activeWorkers.delete(workerId);
        reject(error);
      });
  });
}

/**
 * Intelligent Python execution router
 * Automatically chooses between worker thread and main thread based on feature flag
 */
function runPythonScriptSmart(scriptPath, args = [], event, options = {}) {
  if (USE_WORKER_THREADS) {
    console.log(`[SMART-ROUTER] Using worker thread for: ${path.basename(scriptPath)}`);
    return runPythonScriptInWorker(scriptPath, args, event, options);
  } else {
    console.log(`[SMART-ROUTER] Using main thread for: ${path.basename(scriptPath)}`);
    return runPythonScriptWithRealTimeUpdates(scriptPath, args, event);
  }
}

// ============================================================================
// APPLICATION SETTINGS MANAGEMENT
// ============================================================================

/**
 * Load application settings from file
 */
async function loadAppSettings() {
  try {
    const db = await DatabaseManager.getInstance();
    const settings = await db.getAllQuery(
      'SELECT setting_key, setting_value FROM system_settings WHERE module_name = ?',
      ['system']
    );

    if (settings.length > 0) {
      const settingsObj = {};
      settings.forEach(setting => {
        const keys = setting.setting_key.split('.');
        let current = settingsObj;
        for (let i = 0; i < keys.length - 1; i++) {
          if (!current[keys[i]]) current[keys[i]] = {};
          current = current[keys[i]];
        }
        current[keys[keys.length - 1]] = JSON.parse(setting.setting_value);
      });

      // Apply worker thread setting
      if (settingsObj.performance && typeof settingsObj.performance.enableWorkerThreads === 'boolean') {
        USE_WORKER_THREADS = settingsObj.performance.enableWorkerThreads;
        console.log(`[SETTINGS] Worker threads ${USE_WORKER_THREADS ? 'enabled' : 'disabled'} from database`);
      }

      // Don't close the database - it's needed by other parts of the application
      return settingsObj;
    }

    // Don't close the database - it's needed by other parts of the application
  } catch (error) {
    console.error('[SETTINGS] Error loading settings from database:', error);
  }

  // Return default settings and save them to database
  const defaults = getDefaultSettings();
  await saveAppSettings(defaults);
  return defaults;
}

/**
 * Get default application settings
 */
function getDefaultSettings() {
  return {
    performance: {
      enableWorkerThreads: true,
      workerTimeout: 30,
      batchThreshold: 100,
      enableRealTimeUpdates: true
    },
    reports: {
      autoGenerateReports: true,
      includeSummaryStats: true
    },
    notifications: {
      enableNotifications: true,
      enableSoundAlerts: true
    }
  };
}

/**
 * Save application settings to file
 */
async function saveAppSettings(settings) {
  try {
    const db = await DatabaseManager.getInstance();

    // Apply worker thread setting immediately
    if (settings.performance && typeof settings.performance.enableWorkerThreads === 'boolean') {
      USE_WORKER_THREADS = settings.performance.enableWorkerThreads;
      console.log(`[SETTINGS] Worker threads ${USE_WORKER_THREADS ? 'enabled' : 'disabled'} - effective immediately`);
    }

    // Flatten settings object and save to database
    const flattenSettings = (obj, prefix = '') => {
      const flattened = [];
      for (const [key, value] of Object.entries(obj)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          flattened.push(...flattenSettings(value, fullKey));
        } else {
          flattened.push({ key: fullKey, value: JSON.stringify(value) });
        }
      }
      return flattened;
    };

    const flatSettings = flattenSettings(settings);

    // Clear existing settings
    await db.runQuery('DELETE FROM system_settings WHERE module_name = ?', ['system']);

    // Insert new settings
    for (const setting of flatSettings) {
      await db.runQuery(
        'INSERT INTO system_settings (setting_key, setting_value, module_name) VALUES (?, ?, ?)',
        [setting.key, setting.value, 'system']
      );
    }

    // Don't close the database - it's needed by other parts of the application
    console.log('[SETTINGS] Settings saved to database successfully');
    return true;
  } catch (error) {
    console.error('[SETTINGS] Error saving settings to database:', error);
    return false;
  }
}

// Settings IPC handlers
ipcMain.handle('get-app-settings', async () => {
  try {
    const settings = await loadAppSettings();
    console.log('[SETTINGS] Settings loaded from database for frontend');
    return { success: true, settings };
  } catch (error) {
    console.error('[SETTINGS] Error getting settings from database:', error);
    return { success: false, error: error.message, settings: getDefaultSettings() };
  }
});

// ============================================================================
// DATA BUILDER API HANDLERS
// ============================================================================

// Get dictionary data for Data Builder
ipcMain.handle('get-dictionary-data', async () => {
  try {
    console.log('[DATA-BUILDER] Getting dictionary data...');

    const pythonPath = path.join(__dirname, 'core', 'dictionary_manager.py');
    const result = await runHybridScript(pythonPath, ['get-all-items']);

    const dictionaryData = JSON.parse(result);
    console.log('[DATA-BUILDER] Dictionary data loaded successfully');

    return dictionaryData;
  } catch (error) {
    console.error('[DATA-BUILDER] Error getting dictionary data:', error);

    // Return sample data structure for development
    return {
      'Personal Details': [
        { label: 'Employee No.', type: 'Text', status: 'fixed', frequency: 'Always', includeInReport: true },
        { label: 'Employee Name', type: 'Text', status: 'fixed', frequency: 'Always', includeInReport: true },
        { label: 'Department', type: 'Text', status: 'fixed', frequency: 'Always', includeInReport: true }
      ],
      'Earnings': [
        { label: 'BASIC SALARY', type: 'Currency', status: 'fixed', frequency: 'Always', includeInReport: true },
        { label: 'GROSS SALARY', type: 'Currency', status: 'fixed', frequency: 'Always', includeInReport: true },
        { label: 'NET PAY', type: 'Currency', status: 'fixed', frequency: 'Always', includeInReport: true }
      ],
      'Deductions': [
        { label: 'TOTAL DEDUCTIONS', type: 'Currency', status: 'fixed', frequency: 'Always', includeInReport: true },
        { label: 'INCOME TAX', type: 'Currency', status: 'fixed', frequency: 'Always', includeInReport: true },
        { label: 'TAXABLE SALARY', type: 'Currency', status: 'fixed', frequency: 'Always', includeInReport: true }
      ]
    };
  }
});

// Build spreadsheet from payroll data
ipcMain.handle('build-spreadsheet', async (event, buildConfig) => {
  try {
    console.log('[DATA-BUILDER] Starting spreadsheet build...');
    console.log('[DATA-BUILDER] Build config:', buildConfig);

    // Create Python script for Data Builder
    const pythonPath = path.join(__dirname, 'core', 'data_builder.py');

    // Prepare arguments for Python script
    const args = [
      '--file-path', buildConfig.filePath,
      '--month-year', buildConfig.monthYear,
      '--selected-items', JSON.stringify(buildConfig.selectedItems),
      '--include-analytics', buildConfig.includeAnalytics ? 'true' : 'false',
      '--include-varying', buildConfig.includeVaryingItems ? 'true' : 'false'
    ];

    // Run the data builder script with real-time updates
    const result = await runPythonScriptSmart(pythonPath, args, event, {
      timeout: 10 * 60 * 1000 // 10 minutes timeout
    });

    console.log('[DATA-BUILDER] Raw result:', result);
    console.log('[DATA-BUILDER] Raw result type:', typeof result);
    console.log('[DATA-BUILDER] Raw result length:', result ? result.length : 'null');

    // Parse the result
    let parsedResult;
    try {
      // Check if result is empty or invalid
      if (!result || result.trim() === '') {
        throw new Error('Empty result from Data Builder script');
      }

      // Try to clean the result if it has extra output
      let cleanResult = result.trim();

      // Split by lines and find the JSON result
      const lines = cleanResult.split('\n');
      let jsonResult = null;

      // Look for lines that start with { and try to parse them as JSON
      for (let i = lines.length - 1; i >= 0; i--) {
        const line = lines[i].trim();
        if (line.startsWith('{')) {
          try {
            // Try to parse this line as JSON
            jsonResult = JSON.parse(line);
            cleanResult = line;
            break;
          } catch (e) {
            // If single line fails, try to build multi-line JSON
            let multiLineJson = '';
            let braceCount = 0;
            let foundStart = false;

            for (let j = i; j < lines.length; j++) {
              const currentLine = lines[j].trim();
              if (currentLine.includes('{') || foundStart) {
                foundStart = true;
                multiLineJson += currentLine + '\n';

                // Count braces to find complete JSON
                for (const char of currentLine) {
                  if (char === '{') braceCount++;
                  if (char === '}') braceCount--;
                }

                if (braceCount === 0 && foundStart) {
                  try {
                    jsonResult = JSON.parse(multiLineJson.trim());
                    cleanResult = multiLineJson.trim();
                    break;
                  } catch (parseError) {
                    // Continue searching
                  }
                }
              }
            }
            if (jsonResult) break;
          }
        }
      }

      // If no valid JSON found, use the original approach as fallback
      if (!jsonResult) {
        const jsonStart = cleanResult.lastIndexOf('{');
        const jsonEnd = cleanResult.lastIndexOf('}');

        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
          cleanResult = cleanResult.substring(jsonStart, jsonEnd + 1);
        }
      }

      console.log('[DATA-BUILDER] Cleaned result:', cleanResult);
      parsedResult = JSON.parse(cleanResult);
    } catch (parseError) {
      console.error('[DATA-BUILDER] Failed to parse result:', parseError);
      console.error('[DATA-BUILDER] Raw result that failed to parse:', result);
      throw new Error(`Failed to parse spreadsheet build result: ${parseError.message}`);
    }

    console.log('[DATA-BUILDER] Spreadsheet build completed successfully');
    return parsedResult;

  } catch (error) {
    console.error('[DATA-BUILDER] Error building spreadsheet:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Helper function to build spreadsheet from extracted data
async function buildSpreadsheetFromExtractedData(extractedData, selectedItems, monthYear) {
  try {
    console.log('[DATA-BUILDER] Building spreadsheet from extracted data...');
    console.log('[DATA-BUILDER] Total employees to process:', extractedData.length);

    // Debug raw extracted data structure
    if (extractedData.length > 0) {
      const firstEmployee = extractedData[0];
      console.log('[DATA-BUILDER] ===== RAW EXTRACTED DATA STRUCTURE =====');
      console.log('[DATA-BUILDER] First employee structure:', Object.keys(firstEmployee));

      // Show each section's content
      Object.keys(firstEmployee).forEach(sectionName => {
        const sectionData = firstEmployee[sectionName];
        if (typeof sectionData === 'object' && sectionData !== null) {
          console.log(`[DATA-BUILDER] ${sectionName} section items:`, Object.keys(sectionData));
          console.log(`[DATA-BUILDER] ${sectionName} sample data:`, Object.entries(sectionData).slice(0, 3));
        } else {
          console.log(`[DATA-BUILDER] ${sectionName}: ${sectionData} (${typeof sectionData})`);
        }
      });
    }

    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Payroll Data');

    // Process extracted data to flatten section-based structure from Perfect Section-Aware Extractor
    const processedData = extractedData.map((employee, index) => {
      const flatEmployee = {};

      console.log(`[DATA-BUILDER] Processing employee ${index + 1}:`, Object.keys(employee));

      // Handle Perfect Section-Aware Extractor's section-based structure
      if (typeof employee === 'object' && employee !== null) {

        // CRITICAL: Extract ALL items from ALL sections (both dictionary and non-dictionary items)
        const sectionNames = ['PERSONAL DETAILS', 'EARNINGS', 'DEDUCTIONS', 'EMPLOYERS CONTRIBUTION', 'EMPLOYEE BANK DETAILS', 'LOANS'];

        sectionNames.forEach(sectionName => {
          if (employee[sectionName] && typeof employee[sectionName] === 'object') {
            const sectionItems = employee[sectionName];

            // Extract ALL items from this section with CONSISTENT SECTION PREFIXES
            Object.keys(sectionItems).forEach(itemKey => {
              const itemValue = sectionItems[itemKey];

              // Debug logging for first employee
              if (index === 0) {
                console.log(`[DATA-BUILDER] ${sectionName}.${itemKey} = "${itemValue}" (${typeof itemValue})`);
              }

              // STRATEGY: Use section prefixes across the board for complete differentiation
              const prefixedKey = `${sectionName}.${itemKey}`;
              flatEmployee[prefixedKey] = itemValue;

              // ALSO: Keep unprefixed version for essential/common items (backward compatibility)
              const essentialItems = ['Employee No.', 'Employee Name', 'Department', 'Section', 'Job Title',
                                    'BASIC SALARY', 'GROSS SALARY', 'NET PAY', 'TOTAL DEDUCTIONS', 'TAXABLE SALARY'];

              if (essentialItems.some(essential => itemKey.toUpperCase().includes(essential.toUpperCase()))) {
                flatEmployee[itemKey] = itemValue;
                if (index === 0) {
                  console.log(`[DATA-BUILDER] Essential item - also kept unprefixed: ${itemKey}`);
                }
              }

              if (index === 0) {
                console.log(`[DATA-BUILDER] Created prefixed column: ${prefixedKey}`);
              }
            });

            console.log(`[DATA-BUILDER] Extracted from ${sectionName}:`, Object.keys(sectionItems));
          }
        });

        // Handle any direct properties (fallback for different data structures)
        Object.keys(employee).forEach(key => {
          if (!sectionNames.includes(key)) {
            if (typeof employee[key] === 'object' && employee[key] !== null) {
              // Handle nested objects by flattening with section prefix
              Object.keys(employee[key]).forEach(nestedKey => {
                const flatKey = `${key}_${nestedKey}`;
                flatEmployee[flatKey] = employee[key][nestedKey];
              });
            } else {
              // Direct property
              flatEmployee[key] = employee[key];
            }
          }
        });
      }

      const extractedKeys = Object.keys(flatEmployee);
      console.log(`[DATA-BUILDER] Employee ${index + 1} total extracted items: ${extractedKeys.length}`);
      console.log(`[DATA-BUILDER] Sample keys:`, extractedKeys.slice(0, 10));

      return flatEmployee;
    });

    console.log('[DATA-BUILDER] Sample processed data:', processedData.slice(0, 2));

    // Get all unique columns from processed data
    const allColumns = new Set();
    processedData.forEach(employee => {
      Object.keys(employee).forEach(key => {
        allColumns.add(key);
      });
    });

    console.log('[DATA-BUILDER] Total unique columns discovered:', allColumns.size);
    console.log('[DATA-BUILDER] All discovered columns:', Array.from(allColumns));

    // Essential columns that should always be first (unprefixed versions)
    const essentialColumns = ['Employee No.', 'Employee Name', 'Department', 'Section', 'Job Title',
                              'BASIC SALARY', 'GROSS SALARY', 'NET PAY', 'TOTAL DEDUCTIONS', 'TAXABLE SALARY'];

    // REQUIREMENT: Include ALL extracted items with section-based organization
    let columnsToInclude = Array.from(allColumns);
    let personalDetailsColumns = [];
    let earningsColumns = [];
    let deductionsColumns = [];
    let loansColumns = [];
    let employersContribColumns = [];
    let bankDetailsColumns = [];
    let unprefixedColumns = [];

    console.log('[DATA-BUILDER] TOTAL EXTRACTED COLUMNS:', columnsToInclude.length);

    // Organize columns by section prefixes for better structure
    columnsToInclude.forEach(col => {
      if (col.startsWith('PERSONAL DETAILS.')) {
        personalDetailsColumns.push(col);
      } else if (col.startsWith('EARNINGS.')) {
        earningsColumns.push(col);
      } else if (col.startsWith('DEDUCTIONS.')) {
        deductionsColumns.push(col);
      } else if (col.startsWith('LOANS.')) {
        loansColumns.push(col);
      } else if (col.startsWith('EMPLOYERS CONTRIBUTION.')) {
        employersContribColumns.push(col);
      } else if (col.startsWith('EMPLOYEE BANK DETAILS.')) {
        bankDetailsColumns.push(col);
      } else {
        // Unprefixed columns (essential items, page numbers, etc.)
        unprefixedColumns.push(col);
      }
    });

    console.log('[DATA-BUILDER] Column organization:');
    console.log(`  - Personal Details: ${personalDetailsColumns.length}`);
    console.log(`  - Earnings: ${earningsColumns.length}`);
    console.log(`  - Deductions: ${deductionsColumns.length}`);
    console.log(`  - Loans: ${loansColumns.length}`);
    console.log(`  - Employers Contribution: ${employersContribColumns.length}`);
    console.log(`  - Bank Details: ${bankDetailsColumns.length}`);
    console.log(`  - Unprefixed: ${unprefixedColumns.length}`);

    // Build final column order: Essential → Section-based organization for complete coverage
    const finalColumns = [
      // 1. Essential columns first (unprefixed for easy access)
      ...essentialColumns.filter(col => allColumns.has(col)),

      // 2. Personal Details section (prefixed)
      ...personalDetailsColumns.filter(col => !essentialColumns.includes(col.split('.').pop())),

      // 3. Earnings section (prefixed)
      ...earningsColumns.filter(col => !essentialColumns.includes(col.split('.').pop())),

      // 4. Deductions section (prefixed)
      ...deductionsColumns.filter(col => !essentialColumns.includes(col.split('.').pop())),

      // 5. Loans section (prefixed)
      ...loansColumns,

      // 6. Employers Contribution section (prefixed)
      ...employersContribColumns,

      // 7. Bank Details section (prefixed)
      ...bankDetailsColumns.filter(col => !essentialColumns.includes(col.split('.').pop())),

      // 8. Any remaining unprefixed columns
      ...unprefixedColumns.filter(col => !essentialColumns.includes(col))
    ];

    // CRITICAL VALIDATION: Ensure 100% column coverage with ZERO data loss
    const totalExtractedColumns = allColumns.size;
    const totalFinalColumns = finalColumns.length;
    const missingColumns = Array.from(allColumns).filter(col => !finalColumns.includes(col));

    console.log('[DATA-BUILDER] ===== COLUMN COVERAGE VALIDATION =====');
    console.log(`[DATA-BUILDER] Total extracted columns: ${totalExtractedColumns}`);
    console.log(`[DATA-BUILDER] Total final columns: ${totalFinalColumns}`);
    console.log('[DATA-BUILDER] Section-based column breakdown:');
    console.log(`  - Essential (unprefixed): ${essentialColumns.filter(col => allColumns.has(col)).length}`);
    console.log(`  - Personal Details: ${personalDetailsColumns.length}`);
    console.log(`  - Earnings: ${earningsColumns.length}`);
    console.log(`  - Deductions: ${deductionsColumns.length}`);
    console.log(`  - Loans: ${loansColumns.length}`);
    console.log(`  - Employers Contribution: ${employersContribColumns.length}`);
    console.log(`  - Bank Details: ${bankDetailsColumns.length}`);
    console.log(`  - Other Unprefixed: ${unprefixedColumns.filter(col => !essentialColumns.includes(col)).length}`);

    // CRITICAL CHECK: Ensure no data loss
    if (missingColumns.length > 0) {
      console.error('[DATA-BUILDER] ❌ CRITICAL ERROR: Missing columns detected!');
      console.error('[DATA-BUILDER] Missing columns:', missingColumns);

      // Add missing columns to prevent data loss
      finalColumns.push(...missingColumns);
      console.log('[DATA-BUILDER] ✅ Added missing columns to prevent data loss');
    }

    // FINAL VALIDATION
    const finalValidation = finalColumns.length === allColumns.size;
    console.log(`[DATA-BUILDER] ===== FINAL VALIDATION: ${finalValidation ? '✅ PASS' : '❌ FAIL'} =====`);
    console.log(`[DATA-BUILDER] Coverage: ${finalColumns.length}/${allColumns.size} columns (${((finalColumns.length/allColumns.size)*100).toFixed(1)}%)`);

    if (!finalValidation) {
      throw new Error(`Column coverage validation failed: ${finalColumns.length}/${allColumns.size} columns`);
    }

    // Create headers
    worksheet.addRow(finalColumns);

    // Style headers with better formatting
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF1a237e' }
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };
    headerRow.height = 25;

    // Add borders to headers
    headerRow.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });

    // Add data rows with comprehensive debugging
    console.log('[DATA-BUILDER] ===== DATA POPULATION DEBUGGING =====');

    // Debug first employee in detail
    if (processedData.length > 0) {
      const firstEmployee = processedData[0];
      const employeeKeys = Object.keys(firstEmployee);

      console.log('[DATA-BUILDER] First employee data keys:', employeeKeys.length);
      console.log('[DATA-BUILDER] Sample employee keys:', employeeKeys.slice(0, 20));

      // Check column vs data key mismatches
      const missingDataKeys = finalColumns.filter(col => !(col in firstEmployee));
      const extraDataKeys = employeeKeys.filter(key => !finalColumns.includes(key));

      console.log('[DATA-BUILDER] Columns missing data:', missingDataKeys.length);
      if (missingDataKeys.length > 0) {
        console.log('[DATA-BUILDER] Missing data for columns:', missingDataKeys.slice(0, 10));
      }

      console.log('[DATA-BUILDER] Extra data keys not in columns:', extraDataKeys.length);
      if (extraDataKeys.length > 0) {
        console.log('[DATA-BUILDER] Extra data keys:', extraDataKeys.slice(0, 10));
      }

      // Sample data population check
      console.log('[DATA-BUILDER] Sample data population:');
      finalColumns.slice(0, 10).forEach(col => {
        const value = firstEmployee[col];
        console.log(`  ${col}: "${value}" (${typeof value})`);
      });
    }

    processedData.forEach((employee, index) => {
      const row = finalColumns.map(col => {
        const value = employee[col];

        // Debug empty values for first few employees
        if (index < 3 && (value === undefined || value === null || value === '')) {
          console.log(`[DATA-BUILDER] Employee ${index + 1} missing data for column: ${col}`);
        }

        return value || '';
      });

      const dataRow = worksheet.addRow(row);

      // Alternate row colors for better readability
      if (index % 2 === 1) {
        dataRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFF8F9FA' }
        };
      }

      // Add borders to data cells
      dataRow.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
        cell.alignment = { vertical: 'middle' };
      });
    });

    // Auto-fit columns with better sizing
    worksheet.columns.forEach((column, index) => {
      const columnLetter = String.fromCharCode(65 + index);
      const columnData = worksheet.getColumn(columnLetter);

      // Calculate optimal width
      let maxLength = finalColumns[index]?.length || 10;
      columnData.eachCell({ includeEmpty: false }, (cell) => {
        const cellLength = cell.value ? cell.value.toString().length : 0;
        if (cellLength > maxLength) {
          maxLength = cellLength;
        }
      });

      // Set width with min/max constraints
      column.width = Math.min(Math.max(maxLength + 2, 12), 50);
    });

    // Freeze the first row (headers) and first 3 columns (Employee No, Name, Department)
    worksheet.views = [
      {
        state: 'frozen',
        xSplit: 3,
        ySplit: 1,
        topLeftCell: 'D2',
        activeCell: 'A1'
      }
    ];

    // Create output directory in Report Manager Data Builder folder
    const outputDir = path.join(__dirname, 'data', 'reports', 'Data_Builder_Reports');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Generate filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const fileName = `payroll_data_${monthYear || timestamp}.xlsx`;
    const outputPath = path.join(outputDir, fileName);

    // Save workbook
    await workbook.xlsx.writeFile(outputPath);

    // Calculate analytics
    const analytics = calculateAnalytics(processedData);

    // Save report metadata for Report Manager
    const reportMetadata = {
      id: `data_builder_${timestamp}`,
      type: 'Data Builder Report',
      source_tab: 'data_builder',
      report_type: 'Data_Builder_Spreadsheet',
      report_name: 'System Generated',
      report_designation: 'Data Builder',
      timestamp: new Date().toISOString(),
      month_year: monthYear || new Date().toISOString().slice(0, 7),
      total_employees: processedData.length,
      total_columns: finalColumns.length,
      file_name: fileName,
      file_path: outputPath,
      analytics: analytics,
      selected_items: selectedItems || [],
      processing_time: Date.now()
    };

    // Save metadata to reports folder
    const metadataPath = path.join(outputDir, `${reportMetadata.id}.json`);
    fs.writeFileSync(metadataPath, JSON.stringify(reportMetadata, null, 2));

    console.log('[DATA-BUILDER] ===== FINAL SUMMARY =====');
    console.log('[DATA-BUILDER] Spreadsheet built successfully:', outputPath);
    console.log('[DATA-BUILDER] Report auto-saved to Report Manager');
    console.log(`[DATA-BUILDER] Total employees processed: ${processedData.length}`);
    console.log(`[DATA-BUILDER] Total columns created: ${finalColumns.length}`);
    console.log(`[DATA-BUILDER] File size: ${(fs.statSync(outputPath).size / (1024 * 1024)).toFixed(1)} MB`);

    // Calculate data density
    const totalCells = processedData.length * finalColumns.length;
    let populatedCells = 0;
    processedData.forEach(employee => {
      finalColumns.forEach(col => {
        if (employee[col] && employee[col] !== '') {
          populatedCells++;
        }
      });
    });

    const dataDensity = ((populatedCells / totalCells) * 100).toFixed(1);
    console.log(`[DATA-BUILDER] Data density: ${populatedCells}/${totalCells} cells (${dataDensity}%)`);

    if (parseFloat(dataDensity) < 50) {
      console.warn('[DATA-BUILDER] ⚠️ LOW DATA DENSITY - Many columns may be empty');
    }

    return {
      success: true,
      totalEmployees: processedData.length,
      totalColumns: finalColumns.length,
      processingTime: '0.5',
      fileName: fileName,
      fileSize: (fs.statSync(outputPath).size / (1024 * 1024)).toFixed(1),
      outputPath: outputPath,
      analytics: analytics
    };

  } catch (error) {
    console.error('[DATA-BUILDER] Error building spreadsheet from extracted data:', error);
    throw error;
  }
}

// Helper function to fix known column name mismatches
function fixColumnNameMismatches(originalName) {
  // Known mismatches between raw extracted data and expected column names
  const nameFixes = {
    // Critical mismatches identified in diagnosis
    'SSF EEMPLOYEE': 'SSF EMPLOYEE',
    'SAVING SCHEME (EMPLOYER)': 'SAVING SCHEME EMPLOYER',

    // Bank details fixes
    'Bank': 'Bank',  // Keep as is
    'Account No.': 'Account No.',  // Keep as is
    'Branch': 'Branch',  // Keep as is

    // Common variations
    'EMPLOYEE NO.': 'Employee No.',
    'EMPLOYEE NAME': 'Employee Name',
    'GHANA CARD ID': 'Ghana Card ID',
    'SSF NO.': 'SSF No.',
    'JOB TITLE': 'Job Title',
    'DEPARTMENT': 'Department',
    'SECTION': 'Section'
  };

  // Check for exact match first
  if (nameFixes[originalName]) {
    return nameFixes[originalName];
  }

  // Check for case-insensitive match
  const upperOriginal = originalName.toUpperCase();
  for (const [key, value] of Object.entries(nameFixes)) {
    if (key.toUpperCase() === upperOriginal) {
      return value;
    }
  }

  // Return original if no fix needed
  return originalName;
}

// Helper function to check if a column is a known dictionary item
function isKnownDictionaryItem(columnName) {
  // Common dictionary items (both fixed and auto-learned)
  const knownDictionaryItems = [
    // Personal Details
    'Employee No.', 'Employee Name', 'Department', 'Section', 'Job Title', 'SSF No.', 'Ghana Card ID',

    // Earnings
    'BASIC SALARY', 'GROSS SALARY', 'NET PAY', 'LEAVE ALLOWANCE', 'RENT ELEMENT', 'FUEL ELEMENT',
    'RESPONSIBILITY HEADS', 'UTILITY SUBSIDY',

    // Deductions
    'SSF EMPLOYEE', 'SSF EEMPLOYEE', 'INCOME TAX', 'TAXABLE SALARY', 'TOTAL DEDUCTIONS', 'TITHES',
    'PENT. MIN WELFARE FUND', 'SCHOLARSHIP FUND', 'MINISTERS\' PENSION', 'Loan Deductions',

    // Employers Contribution
    'SSF EMPLOYER', 'SAVING SCHEME (EMPLOYER)', 'SAVING SCHEME EMPLOYER',

    // Bank Details
    'Bank', 'Account No.', 'Branch',

    // Loans (patterns)
    'BALANCE B/F', 'CURRENT DEDUCTION', 'OUST. BALANCE'
  ];

  // Check exact matches first
  if (knownDictionaryItems.includes(columnName)) {
    return true;
  }

  // Check partial matches for common patterns
  const columnUpper = columnName.toUpperCase();

  // Loan patterns
  if (columnUpper.includes('BALANCE B/F') ||
      columnUpper.includes('CURRENT DEDUCTION') ||
      columnUpper.includes('OUST. BALANCE')) {
    return true;
  }

  // Common allowance/deduction patterns
  if (columnUpper.includes('ALLOWANCE') ||
      columnUpper.includes('ELEMENT') ||
      columnUpper.includes('FUND') ||
      columnUpper.includes('PENSION') ||
      columnUpper.includes('WELFARE')) {
    return true;
  }

  return false;
}

// Helper function to calculate analytics
function calculateAnalytics(extractedData) {
  const analytics = {
    totalGrossSalary: 0,
    totalDeductions: 0,
    netPayroll: 0,
    averageSalary: 0,
    totalEmployees: extractedData.length,
    departmentBreakdown: {},
    sectionBreakdown: {}
  };

  let grossSalaryCount = 0;
  let deductionsCount = 0;
  let netPayCount = 0;

  extractedData.forEach(employee => {
    // Department and section breakdown
    const department = employee['Department'] || 'Unknown';
    const section = employee['Section'] || 'Unknown';

    analytics.departmentBreakdown[department] = (analytics.departmentBreakdown[department] || 0) + 1;
    analytics.sectionBreakdown[section] = (analytics.sectionBreakdown[section] || 0) + 1;

    // Look for financial fields with better parsing
    Object.keys(employee).forEach(key => {
      const rawValue = employee[key];
      if (!rawValue) return;

      // Clean and parse numeric values (remove commas, currency symbols)
      const cleanValue = rawValue.toString().replace(/[,\s$]/g, '');
      const value = parseFloat(cleanValue) || 0;

      if (value > 0) {
        if (key.toLowerCase().includes('gross') && key.toLowerCase().includes('salary')) {
          analytics.totalGrossSalary += value;
          grossSalaryCount++;
        } else if (key.toLowerCase().includes('total') && key.toLowerCase().includes('deduction')) {
          analytics.totalDeductions += value;
          deductionsCount++;
        } else if (key.toLowerCase().includes('net') && key.toLowerCase().includes('pay')) {
          analytics.netPayroll += value;
          netPayCount++;
        }
      }
    });
  });

  // Calculate averages
  if (grossSalaryCount > 0) {
    analytics.averageSalary = analytics.totalGrossSalary / grossSalaryCount;
  }

  // Format currency values
  analytics.totalGrossSalary = analytics.totalGrossSalary.toFixed(2);
  analytics.totalDeductions = analytics.totalDeductions.toFixed(2);
  analytics.netPayroll = analytics.netPayroll.toFixed(2);
  analytics.averageSalary = analytics.averageSalary.toFixed(2);

  return analytics;
}

// Cancel spreadsheet build
ipcMain.handle('cancel-spreadsheet-build', async () => {
  try {
    console.log('[DATA-BUILDER] Cancelling spreadsheet build...');

    // Stop any running Python processes
    for (const [processId, process] of currentProcesses) {
      if (process && !process.killed) {
        console.log(`[DATA-BUILDER] Killing process ${processId}`);
        process.kill('SIGTERM');
      }
    }

    // Clear active workers
    for (const [workerId, worker] of activeWorkers) {
      console.log(`[DATA-BUILDER] Terminating worker ${workerId}`);
      // Worker termination would be handled by the worker thread system
    }

    console.log('[DATA-BUILDER] Build cancellation completed');
    return { success: true };

  } catch (error) {
    console.error('[DATA-BUILDER] Error cancelling build:', error);
    return { success: false, error: error.message };
  }
});

// Download generated spreadsheet
ipcMain.handle('download-spreadsheet', async (event, outputPath) => {
  try {
    console.log('[DATA-BUILDER] Downloading spreadsheet:', outputPath);

    if (!fs.existsSync(outputPath)) {
      throw new Error('Spreadsheet file not found');
    }

    // Show save dialog
    const result = await dialog.showSaveDialog(mainWindow, {
      filters: [
        { name: 'Excel Files', extensions: ['xlsx'] },
        { name: 'All Files', extensions: ['*'] }
      ],
      defaultPath: path.basename(outputPath)
    });

    if (result.filePath) {
      // Copy file to selected location
      fs.copyFileSync(outputPath, result.filePath);
      console.log('[DATA-BUILDER] Spreadsheet downloaded to:', result.filePath);
      return { success: true, downloadPath: result.filePath };
    }

    return { success: false, error: 'Download cancelled' };

  } catch (error) {
    console.error('[DATA-BUILDER] Error downloading spreadsheet:', error);
    return { success: false, error: error.message };
  }
});

// Open output folder
ipcMain.handle('open-output-folder', async (event, folderPath) => {
  try {
    console.log('[DATA-BUILDER] Opening output folder:', folderPath);

    const { shell } = require('electron');

    // If specific file path provided, open the containing folder
    if (folderPath && fs.existsSync(folderPath)) {
      if (fs.statSync(folderPath).isFile()) {
        // Open folder containing the file
        shell.showItemInFolder(folderPath);
      } else {
        // Open the folder directly
        shell.openPath(folderPath);
      }
      return { success: true };
    } else {
      // Open default reports folder
      const defaultFolder = path.join(__dirname, 'data', 'reports', 'Data_Builder_Reports');

      // Create folder if it doesn't exist
      if (!fs.existsSync(defaultFolder)) {
        fs.mkdirSync(defaultFolder, { recursive: true });
      }

      shell.openPath(defaultFolder);
      return { success: true };
    }
  } catch (error) {
    console.error('[DATA-BUILDER] Error opening folder:', error);
    return { success: false, error: error.message };
  }
});

// Open output folder
ipcMain.handle('open-folder', async (event, folderPath) => {
  try {
    console.log('[DATA-BUILDER] Opening folder:', folderPath);

    const { shell } = require('electron');

    // If it's a file path, get the directory
    let targetPath = folderPath;
    if (fs.existsSync(folderPath) && fs.statSync(folderPath).isFile()) {
      targetPath = path.dirname(folderPath);
    }

    // Open the folder
    await shell.openPath(targetPath);
    console.log('[DATA-BUILDER] Folder opened successfully');

    return { success: true };

  } catch (error) {
    console.error('[DATA-BUILDER] Error opening folder:', error);
    return { success: false, error: error.message };
  }
});

// PDF Sorter IPC handlers
ipcMain.handle('get-pdf-info', async (event, pdfPath) => {
  try {
    console.log(`[PDF-SORTER] Getting PDF info: ${pdfPath}`);

    const pythonPath = path.join(__dirname, 'core', 'pdf_sorter_integration.py');
    const args = ['--info-only', pdfPath];

    const result = await runPythonScriptSmart(pythonPath, args, event, {
      timeout: 30 * 1000 // 30 seconds timeout
    });

    console.log(`[PDF-SORTER] PDF info raw result:`, result);

    // Parse JSON result from Python script
    let parsedResult;
    try {
      parsedResult = JSON.parse(result);
    } catch (parseError) {
      console.error('[PDF-SORTER] Failed to parse JSON result:', parseError);
      return {
        success: false,
        error: 'Failed to parse PDF info response'
      };
    }

    console.log(`[PDF-SORTER] PDF info parsed result:`, parsedResult);
    return parsedResult;

  } catch (error) {
    console.error('[PDF-SORTER] Error getting PDF info:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

ipcMain.handle('sort-pdf', async (event, pdfPath, sortConfig) => {
  try {
    console.log(`[PDF-SORTER] Starting PDF sorting:`);
    console.log(`[PDF-SORTER]   PDF: ${pdfPath}`);
    console.log(`[PDF-SORTER]   Config:`, sortConfig);

    const pythonPath = path.join(__dirname, 'core', 'pdf_sorter_integration.py');
    const args = [
      pdfPath,
      '--sort-by', sortConfig.primary_sort,
      '--order', sortConfig.sort_order
    ];

    // Add secondary and tertiary sorting if present
    if (sortConfig.secondary_sort && sortConfig.secondary_sort.trim() !== '') {
      args.push('--secondary-sort', sortConfig.secondary_sort);
    }
    if (sortConfig.tertiary_sort && sortConfig.tertiary_sort.trim() !== '') {
      args.push('--tertiary-sort', sortConfig.tertiary_sort);
    }

    console.log(`[PDF-SORTER] Full arguments:`, args);

    // Use smart router for optimal performance (worker threads for non-blocking UI)
    const rawResult = await runPythonScriptSmart(pythonPath, args, event, {
      timeout: 30 * 60 * 1000, // 30 minutes timeout for large files
      progressHandler: (progressData) => {
        // Forward progress updates to frontend
        safeSendToRenderer(event, 'pdf-sorting-progress', progressData);
      }
    });

    console.log(`[PDF-SORTER] Raw result from Python:`, rawResult);

    // Parse JSON result from Python script
    let result;
    try {
      // Clean the result by removing progress messages and extracting JSON
      let cleanResult = rawResult;

      // Split by lines and find the JSON result (last valid JSON object)
      const lines = cleanResult.split('\n');
      let jsonResult = null;

      // Look for the final JSON result (usually the last non-empty line)
      for (let i = lines.length - 1; i >= 0; i--) {
        const line = lines[i].trim();
        if (line && !line.startsWith('PROGRESS:') && !line.startsWith('REALTIME_UPDATE:')) {
          try {
            // Try to parse this line as JSON
            const testParse = JSON.parse(line);
            if (testParse && typeof testParse === 'object') {
              jsonResult = line;
              break;
            }
          } catch (e) {
            // Continue looking for valid JSON
            continue;
          }
        }
      }

      // If no single-line JSON found, try to find JSON block
      if (!jsonResult) {
        // Look for JSON block starting with { and ending with }
        const jsonStart = cleanResult.lastIndexOf('{');
        const jsonEnd = cleanResult.lastIndexOf('}');

        if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
          jsonResult = cleanResult.substring(jsonStart, jsonEnd + 1);
        }
      }

      if (!jsonResult) {
        throw new Error('No valid JSON found in output');
      }

      result = JSON.parse(jsonResult);
    } catch (parseError) {
      console.error('[PDF-SORTER] Failed to parse JSON result:', parseError);
      console.error('[PDF-SORTER] Raw output was:', rawResult);
      return {
        success: false,
        error: `Failed to parse sorting result: ${parseError.message}`,
        raw_output: rawResult
      };
    }

    console.log(`[PDF-SORTER] Sorting completed:`, result.success ? 'SUCCESS' : 'FAILED');
    return result;

  } catch (error) {
    console.error('[PDF-SORTER] Error sorting PDF:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// Get comparison data from database for pre-reporting UI
ipcMain.handle('getComparisonDataFromDatabase', async (event, processId) => {
  try {
    console.log(`[PRE-REPORTING] Fetching comparison data for process ID: ${processId}`);
    
    // Check if data exists in cache to optimize performance
    if (comparisonDataCache.has(processId)) {
      console.log(`[PRE-REPORTING] Using cached comparison data for process ID: ${processId}`);
      return comparisonDataCache.get(processId);
    }
    
    if (!processId) {
      console.error('[PRE-REPORTING] No process ID provided');
      return { success: false, error: 'No process ID provided' };
    }
    
    // First, get session information including months
    const sessionQuery = `
      SELECT current_month, current_year, previous_month, previous_year,
             signature_name, signature_designation
      FROM audit_sessions
      WHERE session_id = ?
    `;

    const sessionRows = await databaseManager.all(sessionQuery, [processId]);
    const sessionInfo = sessionRows && sessionRows.length > 0 ? sessionRows[0] : null;

    // Query database for comparison results joined with employee info
    const query = `
      SELECT
        c.id, c.process_id, c.employee_id, c.item_name, c.section,
        c.previous_value, c.current_value, c.change_type, c.difference,
        c.is_significant, c.priority, c.affected_percentage, c.is_routine_periodic,
        e.employee_no, e.employee_name, e.department
      FROM
        comparison_results c
      JOIN
        employees e ON c.employee_id = e.id
      WHERE
        c.process_id = ?
      ORDER BY
        c.priority, c.section, e.employee_name, c.item_name
    `;

    const rows = await databaseManager.all(query, [processId]);
    
    if (!rows || rows.length === 0) {
      console.log(`[PRE-REPORTING] No comparison data found for process ID: ${processId}`);
      return { success: false, error: 'No comparison data found for this process' };
    }
    
    // Process the raw database results to create properly formatted changes
    const changes = rows.map(row => {
      // Format section name for display (capitalize and clean up)
      const formattedSection = formatSectionName(row.section);
      
      // Generate professional description of the change using preferred terminology
      const description = generateProfessionalChangeDescription(
        row.employee_no,
        row.employee_name,
        row.item_name,
        row.change_type,
        row.previous_value,
        row.current_value,
        row.difference
      );
      
      // Map database priority to display priority (High, Medium, Low)
      let displayPriority = 'Medium';
      if (row.priority === 'high') {
        displayPriority = 'High';
      } else if (row.priority === 'low') {
        displayPriority = 'Low';
      }
      
      // Use section to determine base priority classification
      let sectionPriority = getSectionPriorityClassification(row.section);
      
      // Determine if this is a routine bulk change that should be filtered
      const isRoutineBulkChange = isRoutineChange(row.affected_percentage, row.is_routine_periodic);
      
      // Calculate numeric difference and percentage change with proper formatting
      const numericDifference = calculateNumericDifference(row.previous_value, row.current_value);
      const percentageChange = calculatePercentageChange(row.previous_value, row.current_value);

      // Validate and clean employee name (fix Ghana Card ID issue)
      let cleanEmployeeName = row.employee_name;
      if (!cleanEmployeeName ||
          cleanEmployeeName.toLowerCase().includes('ghana card') ||
          cleanEmployeeName.toLowerCase().includes('card id') ||
          cleanEmployeeName.startsWith('GHA-') ||
          cleanEmployeeName.startsWith('COP') ||
          cleanEmployeeName.startsWith('PW') ||
          cleanEmployeeName.startsWith('SEC') ||
          cleanEmployeeName.startsWith('E') ||
          cleanEmployeeName.startsWith('PGH')) {
        // Use employee number as fallback if name is invalid
        cleanEmployeeName = `EMPLOYEE_${row.employee_no || row.employee_id}`;
      }

      return {
        id: row.id,
        processId: row.process_id,
        employeeId: row.employee_id,
        employeeNo: row.employee_no,
        employeeName: cleanEmployeeName,
        department: row.department || 'UNKNOWN DEPARTMENT',
        itemName: row.item_name,
        section: row.section,
        formattedSection: formattedSection,
        previousValue: formatValue(row.previous_value),
        currentValue: formatValue(row.current_value),
        changeType: row.change_type,
        difference: row.difference,
        numeric_difference: numericDifference,
        percentage_change: percentageChange,
        isSignificant: Boolean(row.is_significant),
        priority: row.priority,
        displayPriority: displayPriority,
        sectionPriority: sectionPriority,
        affectedPercentage: row.affected_percentage,
        isRoutinePeriodic: Boolean(row.is_routine_periodic),
        isRoutineBulkChange: isRoutineBulkChange,
        description: description,
        selected: !isRoutineBulkChange // Pre-select non-routine changes by default
      };
    });
    
    // Filter out routine bulk changes for the default view
    const filteredChanges = changes.filter(change => !change.isRoutineBulkChange);
    
    // Create response object with changes array and session metadata
    const response = {
      success: true,
      processId: processId,
      changes: changes,
      filteredChanges: filteredChanges,
      totalChanges: changes.length,
      significantChanges: filteredChanges.length,
      timestamp: new Date().toISOString(),
      sessionInfo: sessionInfo ? {
        currentMonth: sessionInfo.current_month,
        currentYear: sessionInfo.current_year,
        previousMonth: sessionInfo.previous_month,
        previousYear: sessionInfo.previous_year,
        signatureName: sessionInfo.signature_name,
        signatureDesignation: sessionInfo.signature_designation
      } : null
    };
    
    // Store in cache for future requests
    comparisonDataCache.set(processId, response);
    
    console.log(`[PRE-REPORTING] Successfully retrieved ${changes.length} changes from database, ${filteredChanges.length} non-routine changes`);
    return response;
    
  } catch (error) {
    console.error('[PRE-REPORTING] Error fetching comparison data:', error);
    return { success: false, error: error.message };
  }
});

// Helper function to format section names for display
function formatSectionName(section) {
  if (!section) return 'Uncategorized';
  
  // Convert snake_case or lowercase to Title Case
  return section
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

// Helper function to generate professional change descriptions
function generateProfessionalChangeDescription(employeeNo, employeeName, itemName, changeType, previousValue, currentValue, difference) {
  // Format the employee identifier
  const employeeIdentifier = `${employeeNo}-${employeeName}`;
  
  // Format the values for display
  const prevVal = formatValue(previousValue);
  const currVal = formatValue(currentValue);
  
  // Generate description based on change type using professional terminology
  switch (changeType.toLowerCase()) {
    case 'added':
    case 'new':
      return `${employeeIdentifier}: ${itemName} introduced with value ${currVal}`;
      
    case 'removed':
    case 'deleted':
      return `${employeeIdentifier}: ${itemName} removed (previous value: ${prevVal})`;
      
    case 'increased':
      return `${employeeIdentifier}: ${itemName} changed from ${prevVal} to ${currVal} (increase: ${formatValue(difference)})`;
      
    case 'decreased':
      return `${employeeIdentifier}: ${itemName} changed from ${prevVal} to ${currVal} (decrease: ${formatValue(Math.abs(difference).toFixed(2))})`;
      
    default:
      return `${employeeIdentifier}: ${itemName} changed from ${prevVal} to ${currVal}`;
  }
}

// Helper function to format values for display
function formatValue(value) {
  if (value === null || value === undefined) return 'N/A';

  // Check if value is numeric
  if (!isNaN(value)) {
    // Format as currency if it appears to be a monetary value
    if (value > 100 || value < -100) {
      return parseFloat(value).toFixed(2);
    }
  }

  return value.toString();
}

// Helper function to calculate numeric difference (formatted to 2 decimal places)
function calculateNumericDifference(previousValue, currentValue) {
  const prev = parseFloat(previousValue);
  const curr = parseFloat(currentValue);

  if (isNaN(prev) || isNaN(curr)) {
    return null;
  }

  const difference = parseFloat((curr - prev).toFixed(2));
  return parseFloat(difference.toFixed(2));
}

// Helper function to calculate percentage change (formatted to whole number)
function calculatePercentageChange(previousValue, currentValue) {
  const prev = parseFloat(previousValue);
  const curr = parseFloat(currentValue);

  if (isNaN(prev) || isNaN(curr) || prev === 0) {
    return null;
  }

  const percentageChange = Math.round(((curr - prev) / prev) * 100);
  return Math.round(percentageChange);
}

// Helper function to determine section priority classification based on business rules
function getSectionPriorityClassification(section) {
  if (!section) return 'Medium';
  
  const sectionLower = section.toLowerCase();
  
  // High priority sections
  if (sectionLower.includes('personal') || 
      sectionLower.includes('details') || 
      sectionLower.includes('earning') || 
      sectionLower.includes('deduction')) {
    return 'High';
  }
  
  // Moderate priority sections
  if (sectionLower.includes('loan') || 
      sectionLower.includes('bank') || 
      sectionLower.includes('account')) {
    return 'Medium';
  }
  
  // Low priority sections
  if (sectionLower.includes('employer') || 
      sectionLower.includes('contribution')) {
    return 'Low';
  }
  
  // Default priority
  return 'Medium';
}

// Helper function to determine if a change is a routine bulk change
function isRoutineChange(affectedPercentage, isRoutinePeriodic) {
  // Consider a change routine if it affects a large percentage of employees
  if (affectedPercentage && affectedPercentage > 30) {
    return true;
  }
  
  // Consider a change routine if it's marked as routine periodic
  if (isRoutinePeriodic) {
    return true;
  }
  
  return false;
}

ipcMain.handle('save-temp-file', async (event, fileBuffer, fileName) => {
  try {
    console.log('[PDF-SORTER] Saving temporary file...');

    const fs = require('fs').promises;
    const os = require('os');
    const crypto = require('crypto');

    // Create temp directory if it doesn't exist
    const tempDir = path.join(os.tmpdir(), 'payroll-auditor-temp');
    await fs.mkdir(tempDir, { recursive: true });

    // Generate unique filename
    const timestamp = Date.now();
    const randomId = crypto.randomBytes(8).toString('hex');
    const extension = fileName ? path.extname(fileName) : '.pdf';
    const tempFileName = `pdf_${timestamp}_${randomId}${extension}`;
    const tempFilePath = path.join(tempDir, tempFileName);

    // Save buffer to file
    await fs.writeFile(tempFilePath, Buffer.from(fileBuffer));

    console.log(`[PDF-SORTER] Temporary file saved: ${tempFilePath}`);
    return tempFilePath;

  } catch (error) {
    console.error('[PDF-SORTER] Error saving temporary file:', error);
    return null;
  }
});

ipcMain.handle('save-app-settings', async (event, settings) => {
  try {
    const success = await saveAppSettings(settings);
    if (success) {
      console.log('[SETTINGS] Settings saved to database successfully');
      return { success: true };
    } else {
      return { success: false, error: 'Failed to save settings to database' };
    }
  } catch (error) {
    console.error('[SETTINGS] Error saving settings to database:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('reset-app-settings', async () => {
  try {
    const defaultSettings = getDefaultSettings();
    const success = await saveAppSettings(defaultSettings);
    if (success) {
      console.log('[SETTINGS] Settings reset to defaults in database');
      return { success: true, settings: defaultSettings };
    } else {
      return { success: false, error: 'Failed to reset settings in database' };
    }
  } catch (error) {
    console.error('[SETTINGS] Error resetting settings in database:', error);
    return { success: false, error: error.message };
  }
});

// Load settings on startup
(async () => {
  try {
    await loadAppSettings();
    console.log('[STARTUP] Settings loaded from database');
  } catch (error) {
    console.error('[STARTUP] Error loading settings:', error);
  }
})();

// ============================================================================
// REPORT MANAGER BACKEND HANDLERS
// ============================================================================

// REMOVED: Redundant comparePayrollsEnhanced handler and performPayrollComparison function
// These were duplicates with incomplete integration
// Use compare-payrolls instead (line 541) which has full Perfect Section-Aware Extractor integration

// Get all saved reports from unified database
ipcMain.handle('get-saved-reports', async () => {
  try {
    // Ensure database is available and connected
    if (!databaseManager || !databaseManager.isInitialized) {
      console.error('[REPORTS] Database manager not initialized');
      return { success: false, error: 'Database not initialized', reports: [] };
    }

    if (!databaseManager.isConnected) {
      console.error('[REPORTS] Database not connected');
      return { success: false, error: 'Database not connected', reports: [] };
    }

    console.log('[REPORTS] Loading from unified database...');
    const reportsByCategory = await databaseManager.getAllReports();
    console.log(`[REPORTS] ✅ Loaded ${Object.keys(reportsByCategory).length} report categories from database`);

    // Convert organized reports to flat array for frontend compatibility
    const reports = [];
    const tabs = {};

    for (const [category, categoryReports] of Object.entries(reportsByCategory)) {
      tabs[category] = categoryReports;

      // Add each report to flat array with proper formatting
      categoryReports.forEach(report => {
        // Parse metadata safely
        let parsedMetadata = {};
        try {
          parsedMetadata = typeof report.metadata === 'string' ? JSON.parse(report.metadata) : (report.metadata || {});
        } catch (e) {
          console.warn(`[REPORTS] Could not parse metadata for report ${report.report_id}:`, e);
          parsedMetadata = {};
        }

        // Parse file_paths safely
        let parsedFilePaths = {};
        try {
          parsedFilePaths = typeof report.file_paths === 'string' ? JSON.parse(report.file_paths) : (report.file_paths || {});
        } catch (e) {
          console.warn(`[REPORTS] Could not parse file_paths for report ${report.report_id}:`, e);
          parsedFilePaths = {};
        }

        // Convert database format to frontend format
        const formattedReport = {
          id: report.report_id,
          report_name: report.title,
          report_type: report.report_type,
          tab_folder: category.replace(/\s+/g, '_') + '_Reports',
          created_at: report.created_at,
          timestamp: new Date(report.created_at).getTime() / 1000,
          generated_at: report.created_at,
          file_size: report.file_size,
          metadata: parsedMetadata,
          file_paths: parsedFilePaths,
          description: report.description,
          source_tab: category.toLowerCase().replace(/\s+/g, '_'),
          // Add specific fields from metadata
          ...parsedMetadata
        };

        reports.push(formattedReport);
      });
    }

    console.log(`[REPORTS] ✅ Converted to ${reports.length} reports for frontend`);
    console.log(`[REPORTS] Categories: ${Object.keys(tabs).join(', ')}`);

    return {
      success: true,
      reports,
      tabs,
      totalTabs: Object.keys(tabs).length
    };

  } catch (error) {
    console.error('[REPORT-MANAGER] Error loading reports:', error);
    return { success: false, error: error.message, reports: [] };
  }
});

// View specific report (Database-based)
ipcMain.handle('view-report', async (event, reportId) => {
  try {
    console.log(`[VIEW-REPORT] Attempting to view report: ${reportId}`);

    // Ensure database is available
    if (!databaseManager || !databaseManager.isInitialized || !databaseManager.isConnected) {
      console.error('[VIEW-REPORT] Database not available');
      return { success: false, error: 'Database not available' };
    }

    // Get report from database
    const reportData = await databaseManager.getQuery(
      'SELECT * FROM reports WHERE report_id = ?',
      [reportId]
    );

    if (!reportData) {
      console.error(`[VIEW-REPORT] Report not found in database: ${reportId}`);
      return { success: false, error: 'Report not found' };
    }

    console.log(`[VIEW-REPORT] Found report in database: ${reportData.title}`);

    // Parse file paths
    let filePaths = {};
    try {
      filePaths = JSON.parse(reportData.file_paths || '{}');
    } catch (e) {
      console.warn('[VIEW-REPORT] Could not parse file_paths:', reportData.file_paths);
    }

    const { shell } = require('electron');

    // Handle Bank Adviser Excel reports
    if (reportData.report_category === 'Bank Adviser' && filePaths.excel) {
      console.log(`[VIEW-REPORT] Bank Adviser Excel report - opening: ${filePaths.excel}`);

      if (fs.existsSync(filePaths.excel)) {
        const openResult = await shell.openPath(filePaths.excel);

        if (openResult) {
          console.error(`[VIEW-REPORT] Failed to open Excel: ${openResult}`);
          return { success: false, error: `Failed to open Excel: ${openResult}` };
        }

        console.log(`[VIEW-REPORT] Successfully opened Bank Adviser Excel report`);
        return { success: true, message: 'Bank Adviser Excel report opened successfully' };
      } else {
        console.error(`[VIEW-REPORT] Excel file not found: ${filePaths.excel}`);
        return { success: false, error: 'Excel file not found' };
      }
    }

    // Handle PDF Sorter reports
    if (reportData.report_category === 'PDF Sorter') {
      // Try PDF first, then any other file
      const pdfPath = filePaths.pdf || filePaths.main;
      if (pdfPath && fs.existsSync(pdfPath)) {
        console.log(`[VIEW-REPORT] PDF Sorter report - opening: ${pdfPath}`);

        const openResult = await shell.openPath(pdfPath);
        if (openResult) {
          console.error(`[VIEW-REPORT] Failed to open PDF: ${openResult}`);
          return { success: false, error: `Failed to open PDF: ${openResult}` };
        }

        console.log(`[VIEW-REPORT] Successfully opened PDF`);
        return { success: true, message: 'PDF report opened successfully' };
      }
    }

    // Handle Payroll Audit and Data Builder reports (Word documents)
    if (filePaths.word && fs.existsSync(filePaths.word)) {
      console.log(`[VIEW-REPORT] Opening Word document: ${filePaths.word}`);

      const openResult = await shell.openPath(filePaths.word);

      if (openResult) {
        console.error(`[VIEW-REPORT] Failed to open Word document: ${openResult}`);
        return { success: false, error: `Failed to open Word document: ${openResult}` };
      }

      console.log(`[VIEW-REPORT] Successfully opened Word document`);
      return { success: true, message: 'Report opened successfully' };
    }

    // Handle any other file types - try to open them directly
    const availableFiles = Object.entries(filePaths).filter(([type, path]) => path && fs.existsSync(path));

    if (availableFiles.length > 0) {
      // Use the first available file
      const [fileType, filePath] = availableFiles[0];
      console.log(`[VIEW-REPORT] Opening ${fileType} file: ${filePath}`);

      const openResult = await shell.openPath(filePath);
      if (openResult) {
        console.error(`[VIEW-REPORT] Failed to open ${fileType} file: ${openResult}`);
        return { success: false, error: `Failed to open ${fileType} file: ${openResult}` };
      }

      console.log(`[VIEW-REPORT] Successfully opened ${fileType} file`);
      return { success: true, message: `${fileType} file opened successfully` };
    }

    // If no file paths available, return report data
    console.log(`[VIEW-REPORT] No file paths available, returning report data`);
    console.log(`[VIEW-REPORT] Available file paths:`, filePaths);
    return { success: true, data: reportData, message: 'Report data retrieved' };

  } catch (error) {
    console.error('[VIEW-REPORT] Error viewing report:', error);
    return { success: false, error: error.message };
  }
});

// Download specific report (Database-based)
ipcMain.handle('download-report-by-id', async (event, reportId) => {
  try {
    console.log(`[DOWNLOAD-REPORT-BY-ID] Attempting to download report: ${reportId}`);

    // Ensure database is available
    if (!databaseManager || !databaseManager.isInitialized || !databaseManager.isConnected) {
      console.error('[DOWNLOAD-REPORT-BY-ID] Database not available');
      return { success: false, error: 'Database not available' };
    }

    // Get report from database
    const reportData = await databaseManager.getQuery(
      'SELECT * FROM reports WHERE report_id = ?',
      [reportId]
    );

    if (!reportData) {
      console.error(`[DOWNLOAD-REPORT-BY-ID] Report not found in database: ${reportId}`);
      return { success: false, error: 'Report not found' };
    }

    console.log(`[DOWNLOAD-REPORT-BY-ID] Found report in database: ${reportData.title}`);

    // Parse file paths
    let filePaths = {};
    try {
      filePaths = JSON.parse(reportData.file_paths || '{}');
    } catch (e) {
      console.warn('[DOWNLOAD-REPORT-BY-ID] Could not parse file_paths:', reportData.file_paths);
    }

    const { shell } = require('electron');

    // Handle Bank Adviser Excel reports - just open them
    if (reportData.report_category === 'Bank Adviser' && filePaths.excel) {
      console.log(`[DOWNLOAD-REPORT-BY-ID] Bank Adviser Excel report - opening: ${filePaths.excel}`);

      if (fs.existsSync(filePaths.excel)) {
        const openResult = await shell.openPath(filePaths.excel);

        if (openResult) {
          console.error(`[DOWNLOAD-REPORT-BY-ID] Failed to open Excel: ${openResult}`);
          return { success: false, error: `Failed to open Excel: ${openResult}` };
        }

        console.log(`[DOWNLOAD-REPORT-BY-ID] Successfully opened Bank Adviser Excel report`);
        return { success: true, message: 'Bank Adviser Excel report opened successfully' };
      } else {
        console.error(`[DOWNLOAD-REPORT-BY-ID] Excel file not found: ${filePaths.excel}`);
        return { success: false, error: 'Excel file not found' };
      }
    }

    // Handle PDF Sorter reports - just open them
    if (reportData.report_category === 'PDF Sorter') {
      // Try PDF first, then any other file
      const pdfPath = filePaths.pdf || filePaths.main;
      if (pdfPath && fs.existsSync(pdfPath)) {
        console.log(`[DOWNLOAD-REPORT-BY-ID] PDF Sorter report - opening: ${pdfPath}`);

        const openResult = await shell.openPath(pdfPath);
        if (openResult) {
          console.error(`[DOWNLOAD-REPORT-BY-ID] Failed to open PDF: ${openResult}`);
          return { success: false, error: `Failed to open PDF: ${openResult}` };
        }

        console.log(`[DOWNLOAD-REPORT-BY-ID] Successfully opened PDF`);
        return { success: true, message: 'PDF report opened successfully' };
      }
    }

    // Handle Payroll Audit and Data Builder reports - show save dialog for Word documents
    if (filePaths.word && fs.existsSync(filePaths.word)) {
      console.log(`[DOWNLOAD-REPORT-BY-ID] Showing save dialog for Word document`);

      const result = await dialog.showSaveDialog(mainWindow, {
        filters: [
          { name: 'Word Documents', extensions: ['docx'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        defaultPath: `${reportData.title || 'payroll_report'}.docx`
      });

      if (result.filePath) {
        // Copy the report file to selected location
        fs.copyFileSync(filePaths.word, result.filePath);
        console.log(`[DOWNLOAD-REPORT-BY-ID] Successfully saved report to: ${result.filePath}`);
        return { success: true, filePath: result.filePath, message: 'Report downloaded successfully' };
      } else {
        return { success: false, error: 'No file path selected' };
      }
    }

    // Handle any other file types - try to open them directly
    const availableFiles = Object.entries(filePaths).filter(([type, path]) => path && fs.existsSync(path));

    if (availableFiles.length > 0) {
      // Use the first available file
      const [fileType, filePath] = availableFiles[0];
      console.log(`[DOWNLOAD-REPORT-BY-ID] Opening ${fileType} file: ${filePath}`);

      const openResult = await shell.openPath(filePath);
      if (openResult) {
        console.error(`[DOWNLOAD-REPORT-BY-ID] Failed to open ${fileType} file: ${openResult}`);
        return { success: false, error: `Failed to open ${fileType} file: ${openResult}` };
      }

      console.log(`[DOWNLOAD-REPORT-BY-ID] Successfully opened ${fileType} file`);
      return { success: true, message: `${fileType} file opened successfully` };
    }

    // If no file paths available
    console.log(`[DOWNLOAD-REPORT-BY-ID] No downloadable files available for this report`);
    console.log(`[DOWNLOAD-REPORT-BY-ID] Available file paths:`, filePaths);
    return { success: false, error: 'No downloadable files available for this report' };

  } catch (error) {
    console.error('[DOWNLOAD-REPORT-BY-ID] Error downloading report:', error);
    return { success: false, error: error.message };
  }
});

// Delete specific report (Database-based)
ipcMain.handle('delete-report', async (event, reportId) => {
  try {
    console.log(`[DELETE-REPORT] Attempting to delete report: ${reportId}`);

    // Ensure database is available
    if (!databaseManager || !databaseManager.isInitialized || !databaseManager.isConnected) {
      console.error('[DELETE-REPORT] Database not available');
      return { success: false, error: 'Database not available' };
    }

    // Get report from database first
    const reportData = await databaseManager.getQuery(
      'SELECT * FROM reports WHERE report_id = ?',
      [reportId]
    );

    if (!reportData) {
      console.error(`[DELETE-REPORT] Report not found in database: ${reportId}`);
      return { success: false, error: 'Report not found' };
    }

    console.log(`[DELETE-REPORT] Found report in database: ${reportData.title}`);

    // Parse file paths to delete associated files
    let filePaths = {};
    try {
      filePaths = JSON.parse(reportData.file_paths || '{}');
    } catch (e) {
      console.warn('[DELETE-REPORT] Could not parse file_paths:', reportData.file_paths);
    }

    // Delete associated files
    let deletedFiles = 0;
    for (const [type, filePath] of Object.entries(filePaths)) {
      if (filePath && fs.existsSync(filePath)) {
        try {
          fs.unlinkSync(filePath);
          deletedFiles++;
          console.log(`[DELETE-REPORT] Deleted ${type} file: ${filePath}`);
        } catch (error) {
          console.warn(`[DELETE-REPORT] Could not delete ${type} file: ${filePath}`, error);
        }
      }
    }

    // Delete from database
    await databaseManager.runQuery(
      'DELETE FROM reports WHERE report_id = ?',
      [reportId]
    );

    console.log(`[DELETE-REPORT] Successfully deleted report from database and ${deletedFiles} associated files`);
    return {
      success: true,
      message: `Report deleted successfully (${deletedFiles} files removed)`,
      deletedFiles
    };

  } catch (error) {
    console.error('[DELETE-REPORT] Error deleting report:', error);
    return { success: false, error: error.message };
  }
});

// Export all reports
ipcMain.handle('export-all-reports', async () => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      filters: [
        { name: 'ZIP Archives', extensions: ['zip'] }
      ],
      defaultPath: `all_payroll_reports_${new Date().toISOString().split('T')[0]}.zip`
    });

    if (result.filePath) {
      // This would require a ZIP library - for now, just show success
      console.log(`[REPORT-MANAGER] Would export all reports to: ${result.filePath}`);
      return { success: true, filePath: result.filePath };
    }

    return { success: false, error: 'No file path selected' };

  } catch (error) {
    console.error('[REPORT-MANAGER] Error exporting reports:', error);
    return { success: false, error: error.message };
  }
});

// Stop all running processes (both main thread and worker threads)
ipcMain.handle('stop-payroll-audit', async () => {
  try {
    const totalProcesses = currentProcesses.size + activeWorkers.size;
    console.log(`[STOP-AUDIT] Stopping ${totalProcesses} running processes (${currentProcesses.size} main thread, ${activeWorkers.size} workers)...`);

    let stoppedCount = 0;

    // Stop main thread processes
    for (const [processId, process] of currentProcesses) {
      try {
        process.kill('SIGTERM');
        currentProcesses.delete(processId);
        stoppedCount++;
        console.log(`[STOP-AUDIT] Stopped main thread process ${processId}`);
      } catch (error) {
        console.error(`[STOP-AUDIT] Error stopping main thread process ${processId}:`, error);
      }
    }

    // Stop worker threads
    for (const [workerId, workerInfo] of activeWorkers) {
      try {
        // Note: Worker termination is handled by the worker promise timeout
        // We just remove it from tracking here
        activeWorkers.delete(workerId);
        stoppedCount++;
        console.log(`[STOP-AUDIT] Stopped worker thread ${workerId}`);
      } catch (error) {
        console.error(`[STOP-AUDIT] Error stopping worker thread ${workerId}:`, error);
      }
    }

    console.log(`[STOP-AUDIT] ✅ Stopped ${stoppedCount} processes successfully`);
    return { success: true, stopped_processes: stoppedCount };

  } catch (error) {
    console.error(`[STOP-AUDIT] ❌ Error stopping processes:`, error);
    return { success: false, error: error.message };
  }
});

// ========== BANK ADVISER MODULE IPC HANDLERS ==========

// Debug: Check all reports in database
ipcMain.handle('debug-all-reports', async () => {
  try {
    if (!databaseManager || !databaseManager.isInitialized) {
      return { success: false, error: 'Database not initialized' };
    }

    // Get all reports
    const allReports = await databaseManager.getAllQuery(
      `SELECT * FROM reports ORDER BY created_at DESC`
    );

    // Get report categories summary
    const categorySummary = await databaseManager.getAllQuery(
      `SELECT report_category, COUNT(*) as count FROM reports GROUP BY report_category`
    );

    console.log(`[DEBUG] Found ${allReports.length} total reports in database`);
    console.log(`[DEBUG] Report categories:`, categorySummary);

    return {
      success: true,
      allReports,
      categorySummary,
      totalReports: allReports.length
    };
  } catch (error) {
    console.error('[DEBUG] Error checking all reports:', error);
    return { success: false, error: error.message };
  }
});

// Debug: Check Bank Adviser reports in database
ipcMain.handle('debug-bank-adviser-reports', async () => {
  try {
    if (!databaseManager || !databaseManager.isInitialized) {
      return { success: false, error: 'Database not initialized' };
    }

    // Get all Bank Adviser reports
    const bankAdviserReports = await databaseManager.getAllQuery(
      `SELECT * FROM reports WHERE report_category = 'Bank Adviser' ORDER BY created_at DESC`
    );

    // Get all reports for comparison
    const allReports = await databaseManager.getAllQuery(
      `SELECT report_category, COUNT(*) as count FROM reports GROUP BY report_category`
    );

    console.log(`[DEBUG] Found ${bankAdviserReports.length} Bank Adviser reports`);
    console.log(`[DEBUG] All report categories:`, allReports);

    return {
      success: true,
      bankAdviserReports,
      allReportCategories: allReports,
      totalBankAdviserReports: bankAdviserReports.length
    };
  } catch (error) {
    console.error('[DEBUG] Error checking Bank Adviser reports:', error);
    return { success: false, error: error.message };
  }
});

// Migrate all existing reports to database
ipcMain.handle('migrate-all-reports', async () => {
  try {
    if (!databaseManager || !databaseManager.isInitialized) {
      return { success: false, error: 'Database not initialized' };
    }

    console.log('[MIGRATE-ALL] Starting comprehensive reports migration...');

    const reportsBaseDir = path.join(__dirname, 'data', 'reports');
    let totalMigrated = 0;
    const errors = [];
    const migrationSummary = {};

    // Define report type mappings
    const reportTypeMappings = {
      'Bank Adviser Reports': { category: 'Bank Adviser', extensions: ['.xlsx'], type: 'Excel Bank Advice' },
      'Data_Builder_Reports': { category: 'Data Builder', extensions: ['.xlsx'], type: 'Data Builder Excel' },
      'PDF_Sorter_Reports': { category: 'PDF Sorter', extensions: ['.pdf', '.json'], type: 'PDF Sorter Report' },
      'Payroll Audit': { category: 'Payroll Audit', extensions: ['.json', '.docx', '.pdf'], type: 'Payroll Audit Report' },
      'Payroll_Audit_Reports': { category: 'Payroll Audit', extensions: ['.json', '.docx', '.pdf'], type: 'Payroll Audit Report' }
    };

    // Migrate each report type
    for (const [dirName, config] of Object.entries(reportTypeMappings)) {
      const reportDir = path.join(reportsBaseDir, dirName);

      if (!fs.existsSync(reportDir)) {
        console.log(`[MIGRATE-ALL] Directory not found: ${dirName}`);
        continue;
      }

      console.log(`[MIGRATE-ALL] Processing ${dirName}...`);

      const files = fs.readdirSync(reportDir);
      const relevantFiles = files.filter(file =>
        config.extensions.some(ext => file.endsWith(ext))
      );

      console.log(`[MIGRATE-ALL] Found ${relevantFiles.length} files in ${dirName}`);
      migrationSummary[dirName] = { found: relevantFiles.length, migrated: 0, skipped: 0 };

      for (const fileName of relevantFiles) {
        try {
          const filePath = path.join(reportDir, fileName);
          const fileStats = fs.statSync(filePath);

          // Check if already registered
          const existing = await databaseManager.getQuery(
            `SELECT COUNT(*) as count FROM reports WHERE file_paths LIKE ?`,
            [`%${fileName}%`]
          );

          if (existing.count > 0) {
            console.log(`[MIGRATE-ALL] Skipping ${fileName} - already registered`);
            migrationSummary[dirName].skipped++;
            continue;
          }

          // Generate report ID and metadata
          const reportId = `migrated_${config.category.toLowerCase().replace(/\s+/g, '_')}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

          // Determine file paths based on extension
          const filePaths = {};
          if (fileName.endsWith('.xlsx')) {
            filePaths.excel = filePath;
          } else if (fileName.endsWith('.pdf')) {
            filePaths.pdf = filePath;
          } else if (fileName.endsWith('.docx')) {
            filePaths.word = filePath;
          } else if (fileName.endsWith('.json')) {
            filePaths.json = filePath;
          }

          // Create title based on file type and name
          let title = `${config.type} - ${fileName.replace(/\.[^/.]+$/, "")}`;

          // Register in database
          await databaseManager.runQuery(
            `INSERT INTO reports (report_id, report_type, report_category, title, description, file_paths, metadata, created_at, file_size)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              reportId,
              config.type,
              config.category,
              title,
              `Migrated ${config.category} file: ${fileName}`,
              JSON.stringify(filePaths),
              JSON.stringify({
                fileName: fileName,
                migrated: true,
                originalPath: filePath,
                sourceDirectory: dirName
              }),
              fileStats.birthtime.toISOString(),
              fileStats.size
            ]
          );

          migrationSummary[dirName].migrated++;
          totalMigrated++;
          console.log(`[MIGRATE-ALL] ✅ Migrated: ${fileName} (${config.category})`);

        } catch (error) {
          console.error(`[MIGRATE-ALL] ❌ Error migrating ${fileName}:`, error);
          errors.push({ fileName, directory: dirName, error: error.message });
        }
      }
    }

    console.log(`[MIGRATE-ALL] ✅ Migration completed: ${totalMigrated} files migrated`);
    console.log(`[MIGRATE-ALL] Migration summary:`, migrationSummary);

    return {
      success: true,
      totalMigrated,
      migrationSummary,
      errors,
      message: `Successfully migrated ${totalMigrated} reports across all categories`
    };

  } catch (error) {
    console.error('[MIGRATE-ALL] Migration failed:', error);
    return { success: false, error: error.message };
  }
});

// Migrate existing Bank Adviser Excel files to database (legacy function)
ipcMain.handle('migrate-bank-adviser-reports', async () => {
  // Just call the comprehensive migration function directly
  try {
    if (!databaseManager || !databaseManager.isInitialized) {
      return { success: false, error: 'Database not initialized' };
    }

    console.log('[MIGRATE-BANK-ADVISER] Starting Bank Adviser reports migration...');

    const bankAdviserReportsDir = path.join(__dirname, 'reports', 'Bank Adviser Reports');

    if (!fs.existsSync(bankAdviserReportsDir)) {
      return { success: true, message: 'No Bank Adviser reports directory found', migratedCount: 0 };
    }

    const files = fs.readdirSync(bankAdviserReportsDir);
    const excelFiles = files.filter(file => file.endsWith('.xlsx'));

    console.log(`[MIGRATE-BANK-ADVISER] Found ${excelFiles.length} Excel files to migrate`);

    let migratedCount = 0;
    const errors = [];

    for (const fileName of excelFiles) {
      try {
        const filePath = path.join(bankAdviserReportsDir, fileName);
        const fileStats = fs.statSync(filePath);

        // Check if already registered
        const existing = await databaseManager.getQuery(
          `SELECT COUNT(*) as count FROM reports WHERE file_paths LIKE ?`,
          [`%${fileName}%`]
        );

        if (existing.count > 0) {
          console.log(`[MIGRATE-BANK-ADVISER] Skipping ${fileName} - already registered`);
          continue;
        }

        // Generate report ID and metadata
        const reportId = `bank_advice_migrated_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // Extract session info from filename if possible
        const sessionMatch = fileName.match(/Bank_Advice_(\w+)_(\d{4})/);
        const sessionName = sessionMatch ? `${sessionMatch[1]} ${sessionMatch[2]}` : 'Migrated Session';

        // Register in database
        await databaseManager.runQuery(
          `INSERT INTO reports (report_id, report_type, report_category, title, description, file_paths, metadata, created_at, file_size)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            reportId,
            'Excel Bank Advice',
            'Bank Adviser',
            `Bank Advice Excel - ${sessionName}`,
            `Migrated Bank Advice Excel file: ${fileName}`,
            JSON.stringify({ excel: filePath }),
            JSON.stringify({
              fileName: fileName,
              migrated: true,
              originalPath: filePath,
              sessionName: sessionName
            }),
            fileStats.birthtime.toISOString(),
            fileStats.size
          ]
        );

        migratedCount++;
        console.log(`[MIGRATE-BANK-ADVISER] ✅ Migrated: ${fileName}`);

      } catch (error) {
        console.error(`[MIGRATE-BANK-ADVISER] ❌ Error migrating ${fileName}:`, error);
        errors.push({ fileName, error: error.message });
      }
    }

    console.log(`[MIGRATE-BANK-ADVISER] ✅ Migration completed: ${migratedCount} files migrated`);

    return {
      success: true,
      migratedCount,
      totalFiles: excelFiles.length,
      errors,
      message: `Successfully migrated ${migratedCount} Bank Adviser reports`
    };

  } catch (error) {
    console.error('[MIGRATE-BANK-ADVISER] Migration failed:', error);
    return { success: false, error: error.message };
  }
});

// Bank Adviser Manager instance
let bankAdviserManager = null;

// Initialize Bank Adviser Manager
async function initializeBankAdviserManager() {
  if (!bankAdviserManager) {
    const BankAdviserManager = require('./core/bank_adviser_manager');
    bankAdviserManager = new BankAdviserManager();

    // Wait for initialization with longer timeout
    let retries = 0;
    const maxRetries = 100; // Increased from 50 to 100
    while (!bankAdviserManager.isInitialized && retries < maxRetries) {
      await new Promise(resolve => setTimeout(resolve, 200)); // Increased from 100ms to 200ms
      retries++;

      if (retries % 10 === 0) {
        console.log(`[BANK-ADVISER] Waiting for initialization... (${retries}/${maxRetries})`);
      }
    }

    if (!bankAdviserManager.isInitialized) {
      console.error('[BANK-ADVISER] Manager initialization timeout after', maxRetries * 200, 'ms');
      throw new Error('Bank Adviser Manager initialization timeout');
    }

    console.log('[BANK-ADVISER] Manager initialized successfully');
  }
  return bankAdviserManager;
}

// Create Bank Adviser session
ipcMain.handle('bank-adviser-create-session', async (event, sessionData) => {
  try {
    console.log('[BANK-ADVISER] Creating session:', sessionData);

    const manager = await initializeBankAdviserManager();
    const result = await manager.createSession(
      sessionData.sessionName,
      sessionData.currentMonth,
      sessionData.currentYear,
      sessionData.selectedSections || [],
      sessionData.generationType || 'composite'
    );

    console.log('[BANK-ADVISER] Session creation result:', result);
    return result;
  } catch (error) {
    console.error('[BANK-ADVISER] Session creation failed:', error);
    return { success: false, error: error.message };
  }
});

// Process document (Final Adjusted, Allowances, Awards)
ipcMain.handle('bank-adviser-process-document', async (event, documentData) => {
  try {
    console.log('[BANK-ADVISER] Processing document:', documentData);

    const manager = await initializeBankAdviserManager();
    let result;

    switch (documentData.documentType) {
      case 'final_adjusted':
        result = await manager.processFinalAdjustedPayslip(
          documentData.filePath,
          documentData.fileName,
          documentData.selectedSections || []
        );
        break;
      case 'allowances':
        result = await manager.processAllowancesPayslip(
          documentData.filePath,
          documentData.fileName,
          documentData.taxRate
        );
        break;
      case 'awards':
        result = await manager.processAwardsPayslip(
          documentData.filePath,
          documentData.fileName,
          documentData.taxRate
        );
        break;
      default:
        throw new Error(`Unknown document type: ${documentData.documentType}`);
    }

    console.log('[BANK-ADVISER] Document processing result:', result);
    return { success: result.success, count: result.employeesProcessed || result.allowancesProcessed || result.awardsProcessed };
  } catch (error) {
    console.error('[BANK-ADVISER] Document processing failed:', error);
    return { success: false, error: error.message };
  }
});

// Generate CORRECT Excel Bank Advice - FIXED TO MATCH EXACT REQUIREMENTS
ipcMain.handle('bank-adviser-generate-excel', async (event, generationData) => {
  try {
    console.log('[BANK-ADVISER] Generating CORRECT Excel Bank Advice:', generationData);

    const manager = await initializeBankAdviserManager();

    // Get current session ID
    const sessionId = manager.currentSession?.sessionId;
    if (!sessionId) {
      throw new Error('No active Bank Adviser session found');
    }

    // Use the new CORRECT method that follows exact requirements
    const result = await manager.generateCorrectBankAdviceExcel(
      sessionId,
      generationData.outputPath,
      generationData.generationType || 'composite'
    );

    console.log('[BANK-ADVISER] CORRECT Excel generation result:', result);
    return result;
  } catch (error) {
    console.error('[BANK-ADVISER] CORRECT Excel generation failed:', error);
    return { success: false, error: error.message };
  }
});

// Generate Excel Bank Advice - OLD METHOD (DEPRECATED)
ipcMain.handle('bank-adviser-generate-excel-old', async (event, generationData) => {
  try {
    console.log('[BANK-ADVISER] Generating Excel Bank Advice (OLD):', generationData);

    const manager = await initializeBankAdviserManager();
    let result;

    // Determine which type of bank advice to generate
    switch (generationData.adviceType) {
      case 'final_adjusted':
        result = await manager.generateFinalAdjustedBankAdvice(
          generationData.outputPath,
          generationData.selectedSections || [],
          generationData.generationType || 'composite'
        );
        break;
      case 'allowances':
        result = await manager.generateAllowancesBankAdvice(
          generationData.outputPath,
          generationData.generationType || 'composite'
        );
        break;
      case 'awards':
        result = await manager.generateAwardsBankAdvice(
          generationData.outputPath,
          generationData.generationType || 'composite'
        );
        break;
      default:
        throw new Error(`Unknown advice type: ${generationData.adviceType}`);
    }

    console.log('[BANK-ADVISER] Excel generation result (OLD):', result);
    return result;
  } catch (error) {
    console.error('[BANK-ADVISER] Excel generation failed (OLD):', error);
    return { success: false, error: error.message };
  }
});

// Get tracker data
ipcMain.handle('bank-adviser-get-tracker-data', async (event, queryData) => {
  try {
    console.log('[BANK-ADVISER] Getting tracker data:', queryData);

    const manager = await initializeBankAdviserManager();
    const result = await manager.getTrackerData(
      queryData.category,
      queryData.searchTerm,
      queryData.periodFilter
    );

    return result;
  } catch (error) {
    console.error('[BANK-ADVISER] Get tracker data failed:', error);
    return { success: false, error: error.message };
  }
});

// Export tracker data
ipcMain.handle('bank-adviser-export-tracker', async (event, exportData) => {
  try {
    console.log('[BANK-ADVISER] Exporting tracker data:', exportData);

    const manager = await initializeBankAdviserManager();
    const result = await manager.exportTrackerData(
      exportData.category,
      exportData.searchTerm,
      exportData.periodFilter
    );

    return result;
  } catch (error) {
    console.error('[BANK-ADVISER] Export tracker data failed:', error);
    return { success: false, error: error.message };
  }
});

// Update Bank Adviser session sections (FIXED: Missing handler)
ipcMain.handle('bank-adviser-update-session-sections', async (event, updateData) => {
  try {
    console.log('[BANK-ADVISER] Updating session sections:', updateData);

    const manager = await initializeBankAdviserManager();
    const result = await manager.updateSessionSections(
      updateData.sessionId,
      updateData.selectedSections
    );

    console.log('[BANK-ADVISER] Session sections update result:', result);
    return result;
  } catch (error) {
    console.error('[BANK-ADVISER] Session sections update failed:', error);
    return { success: false, error: error.message };
  }
});

// Get Bank Adviser settings
ipcMain.handle('bank-adviser-get-settings', async () => {
  try {
    const manager = await initializeBankAdviserManager();
    const settings = {
      allowances: manager.taxRates.allowances,
      awards: manager.taxRates.awards,
      educational_subsidy: manager.taxRates.educational_subsidy
    };

    return { success: true, settings };
  } catch (error) {
    console.error('[BANK-ADVISER] Get settings failed:', error);
    return { success: false, error: error.message };
  }
});

// Clear tracker table
ipcMain.handle('bank-adviser-clear-table', async (event, clearData) => {
  try {
    console.log('[BANK-ADVISER] Clearing tracker table:', clearData);

    // Use dedicated Python script for tracker operations
    const pythonPath = path.join(__dirname, 'bank_adviser_tracker_operations.py');
    const args = ['clear_table', clearData.category];

    const result = await runHybridScript(pythonPath, args);
    const parsedResult = JSON.parse(result);

    console.log('[BANK-ADVISER] Clear table result:', parsedResult);
    return parsedResult;
  } catch (error) {
    console.error('[BANK-ADVISER] Clear table failed:', error);
    return { success: false, error: error.message };
  }
});

// Delete selected records from tracker table
ipcMain.handle('bank-adviser-delete-records', async (event, deleteData) => {
  try {
    console.log('[BANK-ADVISER] Deleting tracker records:', deleteData);

    // Use dedicated Python script for tracker operations
    const pythonPath = path.join(__dirname, 'bank_adviser_tracker_operations.py');
    const args = ['delete_records', deleteData.category, ...deleteData.recordIds];

    const result = await runHybridScript(pythonPath, args);
    const parsedResult = JSON.parse(result);

    console.log('[BANK-ADVISER] Delete records result:', parsedResult);
    return parsedResult;
  } catch (error) {
    console.error('[BANK-ADVISER] Delete records failed:', error);
    return { success: false, error: error.message };
  }
});

// Dialog handlers
ipcMain.handle('show-save-dialog', async (event, options) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, options);
    return result;
  } catch (error) {
    console.error('[DIALOG] Save dialog failed:', error);
    return { canceled: true, error: error.message };
  }
});

ipcMain.handle('show-open-dialog', async (event, options) => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, options);
    return result;
  } catch (error) {
    console.error('[DIALOG] Open dialog failed:', error);
    return { canceled: true, error: error.message };
  }
});

// ========================================
// ENHANCED IPC HANDLERS INTEGRATION
// ========================================
// Note: Enhanced IPC handlers are loaded from enhanced_ipc_handlers.js

// ========================================
// PROCESS CONTROL HANDLERS (PAUSE/STOP)
// ========================================

// Process control handlers
ipcMain.handle('pause-enhanced-process', async (event, processId) => {
  try {
    console.log(`[PROCESS-CONTROL] Pausing process ${processId}`);

    if (currentProcesses.has(processId)) {
      const process = currentProcesses.get(processId);

      // Send SIGSTOP to pause the process (Unix-like systems)
      if (process && process.pid) {
        if (require('os').platform() === 'win32') {
          // Windows doesn't support SIGSTOP, so we track pause state
          pausedProcesses.set(processId, true);
          console.log(`[PROCESS-CONTROL] Process ${processId} marked as paused (Windows)`);
        } else {
          process.kill('SIGSTOP');
          pausedProcesses.set(processId, true);
          console.log(`[PROCESS-CONTROL] Process ${processId} paused with SIGSTOP`);
        }
      }
    }

    return { success: true, message: 'Process paused' };
  } catch (error) {
    console.error('[PROCESS-CONTROL] Error pausing process:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('resume-enhanced-process', async (event, processId) => {
  try {
    console.log(`[PROCESS-CONTROL] Resuming process ${processId}`);

    if (currentProcesses.has(processId)) {
      const process = currentProcesses.get(processId);

      // Send SIGCONT to resume the process (Unix-like systems)
      if (process && process.pid) {
        if (require('os').platform() === 'win32') {
          // Windows doesn't support SIGCONT, so we remove pause state
          pausedProcesses.delete(processId);
          console.log(`[PROCESS-CONTROL] Process ${processId} marked as resumed (Windows)`);
        } else {
          process.kill('SIGCONT');
          pausedProcesses.delete(processId);
          console.log(`[PROCESS-CONTROL] Process ${processId} resumed with SIGCONT`);
        }
      }
    }

    return { success: true, message: 'Process resumed' };
  } catch (error) {
    console.error('[PROCESS-CONTROL] Error resuming process:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('stop-enhanced-process', async (event, processId) => {
  try {
    console.log(`[PROCESS-CONTROL] Stopping process ${processId}`);

    if (currentProcesses.has(processId)) {
      const process = currentProcesses.get(processId);

      // Terminate the process
      if (process && process.pid) {
        process.kill('SIGTERM');
        console.log(`[PROCESS-CONTROL] Process ${processId} terminated with SIGTERM`);

        // Clean up tracking
        currentProcesses.delete(processId);
        pausedProcesses.delete(processId);
      }
    }

    return { success: true, message: 'Process stopped' };
  } catch (error) {
    console.error('[PROCESS-CONTROL] Error stopping process:', error);
    return { success: false, error: error.message };
  }
});

console.log('THE PAYROLL AUDITOR - Main process started with Enhanced Features');
