#!/usr/bin/env python3
"""
Fix Process Exit Code Null Error
Comprehensive fix for the critical "Process exited with code null" error
"""

import os
import re

def fix_main_js_process_handling():
    """Fix the process handling in main.js to handle null exit codes"""
    
    print("[FIX] FIXING MAIN.JS PROCESS HANDLING")
    print("=" * 50)
    
    main_js_path = 'main.js'
    if not os.path.exists(main_js_path):
        print("[FAIL] main.js not found")
        return False
    
    try:
        with open(main_js_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix 1: Handle null exit codes in runPythonScriptWithRealTimeUpdates
        old_pattern = r'reject\(new Error\(error \|\| `Process exited with code \$\{code\}`\)\);'
        new_pattern = '''// CRITICAL FIX: Handle null exit codes properly
        const exitCode = code !== null ? code : 'null';
        const errorMessage = error || `Process exited with code ${exitCode}`;
        
        // Don't treat null exit codes as fatal errors if there's output
        if (code === null && finalOutput.trim()) {
          console.warn(`[PYTHON-REALTIME] Process ${processId} exited with null code but has output, treating as success`);
          resolve(finalOutput);
        } else {
          reject(new Error(errorMessage));
        }'''
        
        if re.search(old_pattern, content):
            content = re.sub(old_pattern, new_pattern, content)
            print("   [FIXED] Updated null exit code handling in runPythonScriptWithRealTimeUpdates")
        
        # Fix 2: Handle null exit codes in runHybridScript
        old_pattern2 = r'reject\(new Error\(error \|\| `Process exited with code \$\{code\}`\)\);'
        new_pattern2 = '''// CRITICAL FIX: Handle null exit codes in hybrid script
        const exitCode = code !== null ? code : 'null';
        const errorMessage = error || `Process exited with code ${exitCode}`;
        
        // For hybrid scripts, null exit codes with output should be treated as success
        if (code === null && output.trim()) {
          console.warn(`[HYBRID] Process exited with null code but has output, treating as success`);
          resolve(output);
        } else {
          reject(new Error(errorMessage));
        }'''
        
        # Apply the fix to all instances
        content = re.sub(r'reject\(new Error\(error \|\| `Process exited with code \$\{code\}`\)\);', 
                        new_pattern2, content)
        
        # Fix 3: Add better error handling for Python process startup
        startup_fix = '''
    // CRITICAL FIX: Better Python process startup error handling
    python.on('spawn', () => {
      console.log(`[PYTHON-REALTIME] Process ${processId} spawned successfully`);
    });
    
    python.on('disconnect', () => {
      console.warn(`[PYTHON-REALTIME] Process ${processId} disconnected`);
    });
    
    python.on('exit', (code, signal) => {
      console.log(`[PYTHON-REALTIME] Process ${processId} exited with code: ${code}, signal: ${signal}`);
      currentProcesses.delete(processId);
      
      // Clean up temp file if it exists
      if (tempArgsFile) {
        try {
          const fs = require('fs');
          fs.unlinkSync(tempArgsFile);
          console.log(`[PYTHON-REALTIME] Cleaned up temp args file: ${tempArgsFile}`);
        } catch (cleanupError) {
          console.warn(`[PYTHON-REALTIME] Failed to cleanup temp file: ${cleanupError.message}`);
        }
      }
      
      if (code === 0 || (code === null && finalOutput.trim())) {
        // Success case or null exit with output
        try {
          // Try to parse as JSON first
          if (finalOutput.trim().startsWith('{') || finalOutput.trim().startsWith('[')) {
            const parsed = JSON.parse(finalOutput);
            resolve(parsed);
          } else {
            // Return as string if not JSON
            resolve(finalOutput);
          }
        } catch (parseError) {
          console.warn(`[PYTHON-REALTIME] Output not JSON, returning as string: ${parseError.message}`);
          resolve(finalOutput);
        }
      } else {
        // Error case
        const exitCode = code !== null ? code : 'null';
        const errorMessage = error || `Process exited with code ${exitCode}`;
        
        // Special handling for common Python errors
        if (error.includes('No module named')) {
          reject(new Error(`Python module missing: ${error}`));
        } else if (error.includes('Permission denied')) {
          reject(new Error(`Permission denied: ${error}`));
        } else if (error.includes('File not found')) {
          reject(new Error(`Python script not found: ${error}`));
        } else {
          reject(new Error(errorMessage));
        }
      }
    });'''
        
        # Find the existing python.on('exit') handler and replace it
        exit_handler_pattern = r'python\.on\(\'exit\', \(code\) => \{[^}]+\}\);'
        if re.search(exit_handler_pattern, content, re.DOTALL):
            content = re.sub(exit_handler_pattern, startup_fix.strip(), content, flags=re.DOTALL)
            print("   [FIXED] Enhanced Python process exit handling")
        
        # Fix 4: Add timeout handling to prevent hanging processes
        timeout_fix = '''
    // CRITICAL FIX: Add timeout to prevent hanging processes
    const processTimeout = setTimeout(() => {
      console.warn(`[PYTHON-REALTIME] Process ${processId} timed out after 10 minutes, killing...`);
      if (python && !python.killed) {
        python.kill('SIGTERM');
        setTimeout(() => {
          if (!python.killed) {
            console.warn(`[PYTHON-REALTIME] Force killing process ${processId}`);
            python.kill('SIGKILL');
          }
        }, 5000);
      }
      currentProcesses.delete(processId);
      reject(new Error('Process timed out after 10 minutes'));
    }, 10 * 60 * 1000); // 10 minutes timeout
    
    // Clear timeout when process completes
    python.on('exit', () => {
      clearTimeout(processTimeout);
    });'''
        
        # Add timeout handling after process creation
        process_creation_pattern = r'(const python = spawn\(\'python\', \[scriptPath, \.\.\.argsToUse\], \{[^}]+\}\);)'
        if re.search(process_creation_pattern, content):
            content = re.sub(process_creation_pattern, r'\1\n' + timeout_fix, content)
            print("   [FIXED] Added process timeout handling")
        
        # Write back if changes were made
        if content != original_content:
            with open(main_js_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print("[OK] Applied comprehensive process handling fixes to main.js")
            return True
        else:
            print("[INFO] No changes needed in main.js")
            return True
            
    except Exception as e:
        print(f"[FAIL] Error fixing main.js: {e}")
        return False

def create_python_process_validator():
    """Create a Python script to validate Python environment"""
    
    print("\n[FIX] CREATING PYTHON PROCESS VALIDATOR")
    print("=" * 50)
    
    validator_script = '''#!/usr/bin/env python3
"""
Python Process Validator
Validates that Python environment is working correctly
"""

import sys
import os
import json
from datetime import datetime

def validate_python_environment():
    """Validate Python environment and dependencies"""
    
    validation_result = {
        'success': True,
        'python_version': sys.version,
        'python_executable': sys.executable,
        'working_directory': os.getcwd(),
        'timestamp': datetime.now().isoformat(),
        'issues': []
    }
    
    try:
        # Test basic imports
        import sqlite3
        validation_result['sqlite3_available'] = True
    except ImportError as e:
        validation_result['sqlite3_available'] = False
        validation_result['issues'].append(f'SQLite3 not available: {e}')
        validation_result['success'] = False
    
    try:
        import json
        validation_result['json_available'] = True
    except ImportError as e:
        validation_result['json_available'] = False
        validation_result['issues'].append(f'JSON not available: {e}')
        validation_result['success'] = False
    
    # Test file system access
    try:
        test_file = 'test_write_access.tmp'
        with open(test_file, 'w') as f:
            f.write('test')
        os.remove(test_file)
        validation_result['file_system_access'] = True
    except Exception as e:
        validation_result['file_system_access'] = False
        validation_result['issues'].append(f'File system access issue: {e}')
        validation_result['success'] = False
    
    # Test database access
    try:
        if os.path.exists('payroll_audit.db'):
            import sqlite3
            conn = sqlite3.connect('payroll_audit.db')
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM sqlite_master')
            table_count = cursor.fetchone()[0]
            conn.close()
            validation_result['database_access'] = True
            validation_result['database_tables'] = table_count
        else:
            validation_result['database_access'] = False
            validation_result['issues'].append('Database file not found')
    except Exception as e:
        validation_result['database_access'] = False
        validation_result['issues'].append(f'Database access issue: {e}')
    
    return validation_result

def main():
    """Main validation function"""
    
    if len(sys.argv) > 1 and sys.argv[1] == 'validate':
        result = validate_python_environment()
        print(json.dumps(result, indent=2))
        
        # Exit with appropriate code
        sys.exit(0 if result['success'] else 1)
    else:
        print("Python Process Validator")
        print("Usage: python python_process_validator.py validate")
        sys.exit(0)

if __name__ == "__main__":
    main()
'''
    
    try:
        with open('core/python_process_validator.py', 'w', encoding='utf-8') as f:
            f.write(validator_script)
        
        print("[OK] Python process validator created: core/python_process_validator.py")
        return True
        
    except Exception as e:
        print(f"[FAIL] Error creating Python validator: {e}")
        return False

def test_python_process_fix():
    """Test the Python process fix"""
    
    print("\n[TEST] TESTING PYTHON PROCESS FIX")
    print("=" * 50)
    
    try:
        import subprocess
        import json
        
        # Test the validator script
        result = subprocess.run([
            'python', 'core/python_process_validator.py', 'validate'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            try:
                validation_data = json.loads(result.stdout)
                print("[OK] Python process validation successful:")
                print(f"   Python version: {validation_data.get('python_version', 'Unknown')}")
                print(f"   SQLite3 available: {validation_data.get('sqlite3_available', False)}")
                print(f"   Database access: {validation_data.get('database_access', False)}")
                
                if validation_data.get('issues'):
                    print("   Issues found:")
                    for issue in validation_data['issues']:
                        print(f"     - {issue}")
                
                return validation_data.get('success', False)
                
            except json.JSONDecodeError:
                print(f"[WARN] Validator output not JSON: {result.stdout}")
                return False
        else:
            print(f"[FAIL] Python validator failed with code {result.returncode}")
            print(f"   Error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"[FAIL] Error testing Python process: {e}")
        return False

def main():
    """Main function to run all process exit code fixes"""
    
    print("[FIX] COMPREHENSIVE PROCESS EXIT CODE NULL FIX")
    print("=" * 70)
    
    success = True
    
    # Step 1: Fix main.js process handling
    if not fix_main_js_process_handling():
        success = False
    
    # Step 2: Create Python process validator
    if not create_python_process_validator():
        success = False
    
    # Step 3: Test the fix
    if not test_python_process_fix():
        print("[WARN] Python process test had issues")
    
    if success:
        print("\n[SUCCESS] All process exit code fixes completed!")
        print("The system should now:")
        print("  - Handle null exit codes gracefully")
        print("  - Have better error messages for process failures")
        print("  - Include timeout handling to prevent hanging")
        print("  - Validate Python environment before execution")
    else:
        print("\n[WARN] Some process exit code fixes had issues")
    
    return success

if __name__ == "__main__":
    main()
