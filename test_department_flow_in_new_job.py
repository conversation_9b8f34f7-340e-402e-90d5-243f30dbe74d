#!/usr/bin/env python3
"""
Test Department Flow in New Payroll Audit Job
Check if department information flows through the entire pipeline when running a new job
"""

import os
import sqlite3

def analyze_department_data_flow():
    """Analyze how department data flows through the payroll audit pipeline"""
    
    print("[ANALYSIS] DEPARTMENT DATA FLOW IN PAYROLL AUDIT PIPELINE")
    print("=" * 70)
    
    # Check both databases
    databases = [
        ('Payroll Audit DB', 'payroll_audit.db'),
        ('Bank Adviser DB', 'data/templar_payroll_auditor.db')
    ]
    
    for db_name, db_path in databases:
        print(f"\n{db_name.upper()}:")
        
        if not os.path.exists(db_path):
            print(f"   [MISSING] {db_path}")
            continue
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check if extracted_data table has department info
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='extracted_data'")
            if cursor.fetchone():
                cursor.execute("PRAGMA table_info(extracted_data)")
                columns = [col[1] for col in cursor.fetchall()]
                print(f"   extracted_data columns: {columns}")
                
                if 'department' in columns:
                    cursor.execute("SELECT COUNT(*) FROM extracted_data WHERE department IS NOT NULL AND department != ''")
                    dept_count = cursor.fetchone()[0]
                    print(f"   extracted_data with departments: {dept_count}")
                else:
                    print("   [ISSUE] extracted_data has NO department column")
            
            # Check if comparison_results has department info
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='comparison_results'")
            if cursor.fetchone():
                cursor.execute("PRAGMA table_info(comparison_results)")
                columns = [col[1] for col in cursor.fetchall()]
                print(f"   comparison_results columns: {columns}")
                
                if 'department' in columns:
                    cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE department IS NOT NULL AND department != ''")
                    dept_count = cursor.fetchone()[0]
                    print(f"   comparison_results with departments: {dept_count}")
                else:
                    print("   [ISSUE] comparison_results has NO department column")
            
            # Check tracker_results
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tracker_results'")
            if cursor.fetchone():
                cursor.execute("PRAGMA table_info(tracker_results)")
                columns = [col[1] for col in cursor.fetchall()]
                print(f"   tracker_results columns: {columns}")
                
                if 'department' in columns:
                    cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE department IS NOT NULL AND department != ''")
                    dept_count = cursor.fetchone()[0]
                    print(f"   tracker_results with departments: {dept_count}")
                else:
                    print("   [ISSUE] tracker_results has NO department column")
            
            # Check tracker tables
            tracker_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
            for table in tracker_tables:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = [col[1] for col in cursor.fetchall()]
                    
                    if 'department' in columns:
                        cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE department IS NOT NULL AND department != '' AND department != 'DEPARTMENT NOT SPECIFIED'")
                        dept_count = cursor.fetchone()[0]
                        print(f"   {table} with real departments: {dept_count}")
                    else:
                        print(f"   [ISSUE] {table} has NO department column")
            
            conn.close()
            
        except Exception as e:
            print(f"   [ERROR] {e}")

def check_department_extraction_code():
    """Check if the extraction code properly handles departments"""
    
    print("\n[ANALYSIS] DEPARTMENT EXTRACTION CODE")
    print("=" * 70)
    
    # Check if extraction code includes department
    extraction_files = [
        'core/perfect_section_aware_extractor.py',
        'core/payroll_auditor.py',
        'core/perfect_extraction_integration.py'
    ]
    
    for file_path in extraction_files:
        if os.path.exists(file_path):
            print(f"\n{file_path}:")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for department-related code
                if 'department' in content.lower():
                    print("   ✅ Contains department-related code")
                    
                    # Count occurrences
                    dept_count = content.lower().count('department')
                    print(f"   Department mentions: {dept_count}")
                    
                    # Check for specific patterns
                    if 'PERSONAL DETAILS' in content and 'DEPARTMENT' in content:
                        print("   ✅ Extracts from PERSONAL DETAILS.DEPARTMENT")
                    
                    if 'get_proper_department' in content or 'extract_department' in content:
                        print("   ✅ Has department extraction function")
                else:
                    print("   ❌ No department-related code found")
            except Exception as e:
                print(f"   [ERROR] {e}")
        else:
            print(f"\n{file_path}: [MISSING]")

def check_tracker_feeding_department_flow():
    """Check if tracker feeding preserves department information"""
    
    print("\n[ANALYSIS] TRACKER FEEDING DEPARTMENT FLOW")
    print("=" * 70)
    
    # Check tracker feeding code
    tracker_files = [
        'core/phased_process_manager.py',
        'bank_adviser_tracker_operations.py',
        'core/perfect_extraction_integration.py'
    ]
    
    for file_path in tracker_files:
        if os.path.exists(file_path):
            print(f"\n{file_path}:")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for tracker feeding with department
                if 'INSERT INTO in_house_loans' in content or 'INSERT INTO external_loans' in content:
                    print("   ✅ Contains tracker table insertion code")
                    
                    # Check if department is included in INSERT statements
                    if 'department' in content.lower():
                        print("   ✅ Includes department in tracker feeding")
                    else:
                        print("   ❌ Department NOT included in tracker feeding")
                
                # Check for department extraction in tracker feeding
                if 'get_proper_department' in content or 'extract_department' in content:
                    print("   ✅ Has department extraction in tracker feeding")
                else:
                    print("   ❌ No department extraction in tracker feeding")
                    
            except Exception as e:
                print(f"   [ERROR] {e}")
        else:
            print(f"\n{file_path}: [MISSING]")

def simulate_new_job_department_flow():
    """Simulate what happens to department data in a new job"""
    
    print("\n[SIMULATION] NEW JOB DEPARTMENT FLOW")
    print("=" * 70)
    
    print("EXPECTED FLOW:")
    print("1. PDF Extraction → extracted_data (with department from PERSONAL DETAILS)")
    print("2. Comparison → comparison_results (with department from extracted_data)")
    print("3. Tracker Feeding → tracker_results (with department from comparison_results)")
    print("4. Bank Adviser Population → tracker tables (with department from tracker_results)")
    
    print("\nPOTENTIAL ISSUES:")
    
    # Check if extracted_data includes department
    payroll_db = 'payroll_audit.db'
    if os.path.exists(payroll_db):
        conn = sqlite3.connect(payroll_db)
        cursor = conn.cursor()
        
        # Check extracted_data schema
        cursor.execute("PRAGMA table_info(extracted_data)")
        extracted_columns = [col[1] for col in cursor.fetchall()]
        
        if 'department' not in extracted_columns:
            print("❌ CRITICAL: extracted_data table missing department column")
            print("   → Departments won't be captured during extraction")
        else:
            print("✅ extracted_data has department column")
        
        # Check comparison_results schema
        cursor.execute("PRAGMA table_info(comparison_results)")
        comparison_columns = [col[1] for col in cursor.fetchall()]
        
        if 'department' not in comparison_columns:
            print("❌ CRITICAL: comparison_results table missing department column")
            print("   → Departments won't flow to comparison phase")
        else:
            print("✅ comparison_results has department column")
        
        # Check tracker_results schema
        cursor.execute("PRAGMA table_info(tracker_results)")
        tracker_columns = [col[1] for col in cursor.fetchall()]
        
        if 'department' not in tracker_columns:
            print("❌ CRITICAL: tracker_results table missing department column")
            print("   → Departments won't flow to tracker feeding")
        else:
            print("✅ tracker_results has department column")
        
        conn.close()
    
    # Check Bank Adviser database
    bank_db = 'data/templar_payroll_auditor.db'
    if os.path.exists(bank_db):
        conn = sqlite3.connect(bank_db)
        cursor = conn.cursor()
        
        # Check tracker tables
        tracker_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        for table in tracker_tables:
            cursor.execute(f"PRAGMA table_info({table})")
            columns = [col[1] for col in cursor.fetchall()]
            
            if 'department' not in columns:
                print(f"❌ CRITICAL: {table} missing department column")
                print(f"   → New jobs won't populate {table} with departments")
            else:
                print(f"✅ {table} has department column")
        
        conn.close()

def main():
    """Main analysis function"""
    
    print("[TEST] COMPREHENSIVE DEPARTMENT FLOW ANALYSIS FOR NEW JOBS")
    print("=" * 80)
    
    # Step 1: Analyze current data flow
    analyze_department_data_flow()
    
    # Step 2: Check extraction code
    check_department_extraction_code()
    
    # Step 3: Check tracker feeding
    check_tracker_feeding_department_flow()
    
    # Step 4: Simulate new job flow
    simulate_new_job_department_flow()
    
    print("\n[CONCLUSION] DEPARTMENT FLOW ANALYSIS")
    print("=" * 80)
    
    print("ANSWER TO YOUR QUESTION:")
    print("When you run a NEW payroll audit job, departments SHOULD appear in tracker tables IF:")
    print("1. ✅ Extraction code captures department from PERSONAL DETAILS section")
    print("2. ✅ Comparison phase preserves department information")
    print("3. ✅ Tracker feeding includes department in INSERT statements")
    print("4. ✅ All database tables have department columns")
    
    print("\nIf any of these steps are missing, departments will show as 'DEPARTMENT NOT SPECIFIED'")
    print("The fix I applied earlier only fixed EXISTING data, not the pipeline for NEW jobs.")

if __name__ == "__main__":
    main()
