#!/usr/bin/env python3
"""
Fix Report Generation and Report Manager Integration
Comprehensive fix for report generation issues and Report Manager integration
"""

import os
import sqlite3
import json
from datetime import datetime
import uuid

def create_reports_table():
    """Create reports table if it doesn't exist"""
    
    print("[FIX] CREATING REPORTS TABLE")
    print("=" * 50)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create reports table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_id TEXT UNIQUE NOT NULL,
                report_type TEXT NOT NULL,
                report_category TEXT DEFAULT 'payroll_audit',
                title TEXT NOT NULL,
                description TEXT,
                file_paths TEXT, -- JSON string
                metadata TEXT,   -- JSON string
                file_size INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        print("[OK] Reports table created/verified")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error creating reports table: {e}")
        return False

def generate_sample_report():
    """Generate a sample report from current comparison results"""
    
    print("\n[FIX] GENERATING SAMPLE REPORT")
    print("=" * 50)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute("SELECT session_id FROM sessions ORDER BY created_at DESC LIMIT 1")
        session_result = cursor.fetchone()
        if not session_result:
            print("[FAIL] No session found")
            return False
        
        current_session = session_result[0]
        
        # Get session details
        cursor.execute("""
            SELECT current_month, current_year, previous_month, previous_year 
            FROM sessions 
            WHERE session_id = ?
        """, (current_session,))
        session_details = cursor.fetchone()
        
        if not session_details:
            print("[FAIL] No session details found")
            return False
        
        current_month, current_year, previous_month, previous_year = session_details
        
        # Get comparison results
        cursor.execute("""
            SELECT employee_id, employee_name, department, section, item_label,
                   previous_value, current_value, change_type, priority_level
            FROM comparison_results 
            WHERE session_id = ?
            ORDER BY priority_level DESC, employee_id
        """, (current_session,))
        
        comparison_results = cursor.fetchall()
        
        if not comparison_results:
            print("[FAIL] No comparison results found")
            return False
        
        # Create report data
        report_data = {
            'report_id': f"payroll_audit_{current_month}_{current_year}_{int(datetime.now().timestamp())}",
            'session_id': current_session,
            'report_title': f'Payroll Audit Report - {current_month} {current_year}',
            'current_period': f'{current_month} {current_year}',
            'previous_period': f'{previous_month} {previous_year}',
            'generated_at': datetime.now().isoformat(),
            'total_changes': len(comparison_results),
            'changes': []
        }
        
        # Process comparison results
        for result in comparison_results:
            employee_id, employee_name, department, section, item_label, prev_val, curr_val, change_type, priority = result
            
            change_data = {
                'employee_id': employee_id,
                'employee_name': employee_name,
                'department': department or 'UNKNOWN DEPARTMENT',
                'section': section,
                'item_label': item_label,
                'previous_value': prev_val,
                'current_value': curr_val,
                'change_type': change_type,
                'priority_level': priority
            }
            
            report_data['changes'].append(change_data)
        
        # Create report directory
        reports_dir = 'reports'
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        
        # Save report as JSON
        report_filename = f"{report_data['report_id']}.json"
        report_filepath = os.path.join(reports_dir, report_filename)
        
        with open(report_filepath, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"[OK] Report generated: {report_filepath}")
        
        # Save to reports table
        report_id = str(uuid.uuid4())
        file_paths = {
            'json': report_filepath,
            'excel': report_filepath.replace('.json', '.xlsx'),
            'pdf': report_filepath.replace('.json', '.pdf')
        }
        
        metadata = {
            'session_id': current_session,
            'current_period': f'{current_month} {current_year}',
            'previous_period': f'{previous_month} {previous_year}',
            'total_changes': len(comparison_results),
            'generated_by': 'System',
            'report_format': 'JSON'
        }
        
        cursor.execute("""
            INSERT INTO reports 
            (report_id, report_type, report_category, title, description, 
             file_paths, metadata, file_size)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            report_id,
            'payroll_audit',
            'payroll_audit',
            report_data['report_title'],
            f'Payroll audit report for {current_month} {current_year} with {len(comparison_results)} changes',
            json.dumps(file_paths),
            json.dumps(metadata),
            os.path.getsize(report_filepath)
        ))
        
        conn.commit()
        conn.close()
        
        print(f"[OK] Report saved to database with ID: {report_id}")
        return True
        
    except Exception as e:
        print(f"[FAIL] Error generating sample report: {e}")
        return False

def fix_report_manager_integration():
    """Fix Report Manager integration issues"""
    
    print("\n[FIX] FIXING REPORT MANAGER INTEGRATION")
    print("=" * 50)
    
    # Check if reports directory exists
    reports_dir = 'reports'
    if not os.path.exists(reports_dir):
        os.makedirs(reports_dir)
        print("[OK] Created reports directory")
    
    # Check if backend reports directory exists
    backend_reports_dir = os.path.join('backend-dist', 'reports', 'payroll_audit_reports')
    if not os.path.exists(backend_reports_dir):
        os.makedirs(backend_reports_dir, exist_ok=True)
        print("[OK] Created backend reports directory")
    
    print("[OK] Report Manager integration directories verified")
    return True

def create_report_redirection_script():
    """Create JavaScript code for automatic redirection to Report tab"""
    
    print("\n[FIX] CREATING REPORT REDIRECTION FUNCTIONALITY")
    print("=" * 50)
    
    redirection_js = """
// Report Generation Success Handler with Automatic Redirection
function handleReportGenerationSuccess(reportResult) {
    console.log('📊 Report generation completed successfully:', reportResult);
    
    // Show success notification
    showNotification('Report generated successfully! Redirecting to Report Manager...', 'success');
    
    // Wait 2 seconds then redirect to Report tab
    setTimeout(() => {
        console.log('🔄 Redirecting to Report Manager tab...');
        
        // Switch to Report Manager tab
        const reportTab = document.querySelector('[data-tab="report-manager"]');
        if (reportTab) {
            reportTab.click();
            
            // Refresh the reports list
            setTimeout(() => {
                if (typeof loadSavedReports === 'function') {
                    loadSavedReports();
                }
            }, 500);
        } else {
            console.warn('⚠️ Report Manager tab not found');
        }
    }, 2000);
}

// Enhanced Report Generation Function
async function generateReportWithRedirection(reportData, options = {}) {
    try {
        console.log('📊 Starting report generation with redirection...');
        
        // Call the existing report generation API
        const result = await window.api.generateReport(reportData, options);
        
        if (result.success) {
            // Handle successful generation
            handleReportGenerationSuccess(result);
            
            // Save to Report Manager
            try {
                const saveResult = await window.api.saveToReportManager({
                    report_id: reportData.id || `report_${Date.now()}`,
                    report_type: 'payroll_audit',
                    title: reportData.report_title || 'Payroll Audit Report',
                    description: `Generated on ${new Date().toLocaleDateString()}`,
                    file_paths: result.file_paths || {},
                    metadata: {
                        session_id: reportData.session_id,
                        generated_at: new Date().toISOString(),
                        total_changes: reportData.selected_changes?.length || 0
                    }
                });
                
                if (saveResult.success) {
                    console.log('✅ Report saved to Report Manager successfully');
                } else {
                    console.warn('⚠️ Failed to save to Report Manager:', saveResult.error);
                }
            } catch (saveError) {
                console.error('❌ Error saving to Report Manager:', saveError);
            }
        } else {
            throw new Error(result.error || 'Report generation failed');
        }
        
        return result;
        
    } catch (error) {
        console.error('❌ Report generation with redirection failed:', error);
        showNotification('Report generation failed: ' + error.message, 'error');
        throw error;
    }
}

// Override the existing generateReportsWithProgress function
if (typeof window.generateReportsWithProgress === 'function') {
    window.originalGenerateReportsWithProgress = window.generateReportsWithProgress;
}

window.generateReportsWithProgress = generateReportWithRedirection;

console.log('✅ Report redirection functionality loaded');
"""
    
    # Save the JavaScript code to a file
    js_filepath = 'ui/report_redirection.js'
    with open(js_filepath, 'w', encoding='utf-8') as f:
        f.write(redirection_js)
    
    print(f"[OK] Report redirection script created: {js_filepath}")
    return True

def verify_report_fixes():
    """Verify that all report fixes are working"""
    
    print("\n[CHECK] VERIFYING REPORT FIXES")
    print("=" * 50)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check reports table
        cursor.execute("SELECT COUNT(*) FROM reports")
        report_count = cursor.fetchone()[0]
        print(f"[INFO] Reports in database: {report_count}")
        
        if report_count > 0:
            cursor.execute("SELECT report_id, title, created_at FROM reports ORDER BY created_at DESC LIMIT 3")
            recent_reports = cursor.fetchall()
            print("   Recent reports:")
            for report_id, title, created_at in recent_reports:
                print(f"     - {title} ({created_at})")
        
        # Check directories
        reports_dir = 'reports'
        if os.path.exists(reports_dir):
            report_files = [f for f in os.listdir(reports_dir) if f.endswith('.json')]
            print(f"[INFO] Report files: {len(report_files)}")
        
        # Check redirection script
        js_filepath = 'ui/report_redirection.js'
        if os.path.exists(js_filepath):
            print("[OK] Report redirection script exists")
        else:
            print("[WARN] Report redirection script missing")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error verifying report fixes: {e}")
        return False

def main():
    """Main function to run all report fixes"""
    
    print("[FIX] COMPREHENSIVE REPORT GENERATION & MANAGER INTEGRATION FIX")
    print("=" * 80)
    
    success = True
    
    # Step 1: Create reports table
    if not create_reports_table():
        success = False
    
    # Step 2: Generate sample report
    if not generate_sample_report():
        success = False
    
    # Step 3: Fix Report Manager integration
    if not fix_report_manager_integration():
        success = False
    
    # Step 4: Create redirection functionality
    if not create_report_redirection_script():
        success = False
    
    # Step 5: Verify fixes
    if not verify_report_fixes():
        success = False
    
    if success:
        print("\n[SUCCESS] All report generation and Report Manager fixes completed!")
        print("Reports should now:")
        print("  - Generate properly from pre-reporting data")
        print("  - Save to Report Manager automatically")
        print("  - Redirect to Report tab after generation")
    else:
        print("\n[WARN] Some report fixes had issues")
    
    return success

if __name__ == "__main__":
    main()
