#!/usr/bin/env python3
"""
Emergency Fix Database Schema
Fix the critical database schema issues causing API failures
"""

import os
import sqlite3

def fix_comparison_results_department_column():
    """Add department column to comparison_results table"""
    
    print("[EMERGENCY FIX] ADDING DEPARTMENT COLUMN TO COMPARISON_RESULTS")
    print("=" * 70)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current schema
        cursor.execute("PRAGMA table_info(comparison_results)")
        columns = [col[1] for col in cursor.fetchall()]
        print(f"Current columns: {columns}")
        
        # Add department column if missing
        if 'department' not in columns:
            cursor.execute("ALTER TABLE comparison_results ADD COLUMN department TEXT DEFAULT 'DEPARTMENT NOT SPECIFIED'")
            print("✅ Added department column to comparison_results")
            
            # Update existing records with department info based on employee_id
            cursor.execute("""
                UPDATE comparison_results 
                SET department = CASE 
                    WHEN employee_id LIKE 'COP%' THEN 'POLICE DEPARTMENT'
                    WHEN employee_id LIKE 'PW%' THEN 'PUBLIC WORKS DEPARTMENT'
                    WHEN employee_id LIKE 'MIN%' THEN 'MINISTRY DEPARTMENT'
                    WHEN employee_id LIKE 'FIN%' THEN 'FINANCE DEPARTMENT'
                    WHEN employee_id LIKE 'HR%' THEN 'HUMAN RESOURCES DEPARTMENT'
                    WHEN employee_id LIKE 'ENG%' THEN 'ENGINEERING DEPARTMENT'
                    WHEN employee_id LIKE 'ADMIN%' THEN 'ADMINISTRATION DEPARTMENT'
                    WHEN employee_id LIKE 'IT%' THEN 'INFORMATION TECHNOLOGY DEPARTMENT'
                    ELSE 'DEPARTMENT NOT SPECIFIED'
                END
                WHERE department IS NULL OR department = ''
            """)
            
            updated_count = cursor.rowcount
            print(f"✅ Updated {updated_count} existing records with department info")
        else:
            print("[INFO] Department column already exists")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Error fixing comparison_results: {e}")
        return False

def fix_extracted_data_department_column():
    """Add department column to extracted_data table"""
    
    print("\n[EMERGENCY FIX] ADDING DEPARTMENT COLUMN TO EXTRACTED_DATA")
    print("=" * 70)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if extracted_data table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='extracted_data'")
        if not cursor.fetchone():
            print("[INFO] extracted_data table doesn't exist")
            conn.close()
            return True
        
        # Check current schema
        cursor.execute("PRAGMA table_info(extracted_data)")
        columns = [col[1] for col in cursor.fetchall()]
        print(f"Current columns: {columns}")
        
        # Add department column if missing
        if 'department' not in columns:
            cursor.execute("ALTER TABLE extracted_data ADD COLUMN department TEXT DEFAULT 'DEPARTMENT NOT SPECIFIED'")
            print("✅ Added department column to extracted_data")
            
            # Update existing records
            cursor.execute("""
                UPDATE extracted_data 
                SET department = CASE 
                    WHEN employee_no LIKE 'COP%' THEN 'POLICE DEPARTMENT'
                    WHEN employee_no LIKE 'PW%' THEN 'PUBLIC WORKS DEPARTMENT'
                    WHEN employee_no LIKE 'MIN%' THEN 'MINISTRY DEPARTMENT'
                    WHEN employee_no LIKE 'FIN%' THEN 'FINANCE DEPARTMENT'
                    WHEN employee_no LIKE 'HR%' THEN 'HUMAN RESOURCES DEPARTMENT'
                    WHEN employee_no LIKE 'ENG%' THEN 'ENGINEERING DEPARTMENT'
                    WHEN employee_no LIKE 'ADMIN%' THEN 'ADMINISTRATION DEPARTMENT'
                    WHEN employee_no LIKE 'IT%' THEN 'INFORMATION TECHNOLOGY DEPARTMENT'
                    ELSE 'DEPARTMENT NOT SPECIFIED'
                END
                WHERE department IS NULL OR department = ''
            """)
            
            updated_count = cursor.rowcount
            print(f"✅ Updated {updated_count} existing records with department info")
        else:
            print("[INFO] Department column already exists")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Error fixing extracted_data: {e}")
        return False

def test_pre_reporting_query():
    """Test the pre-reporting query that was failing"""
    
    print("\n[TEST] TESTING PRE-REPORTING QUERY")
    print("=" * 70)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test the query that was failing
        test_query = """
            SELECT id, employee_id, employee_name, department, section, item_label,
                   previous_value, current_value, change_type, priority_level,
                   numeric_difference, percentage_change
            FROM comparison_results 
            ORDER BY priority_level DESC, section, employee_id
            LIMIT 5
        """
        
        cursor.execute(test_query)
        results = cursor.fetchall()
        
        print(f"✅ Query executed successfully, returned {len(results)} results")
        
        if results:
            print("Sample results:")
            for i, result in enumerate(results):
                print(f"   {i+1}. {result[1]}: {result[2]} ({result[3]}) - {result[5]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Pre-reporting query test failed: {e}")
        return False

def fix_bank_adviser_database_schema():
    """Fix Bank Adviser database schema issues"""
    
    print("\n[EMERGENCY FIX] FIXING BANK ADVISER DATABASE SCHEMA")
    print("=" * 70)
    
    db_path = 'data/templar_payroll_auditor.db'
    if not os.path.exists(db_path):
        print("[SKIP] Bank Adviser database not found")
        return True
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Fix comparison_results table in Bank Adviser DB
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='comparison_results'")
        if cursor.fetchone():
            cursor.execute("PRAGMA table_info(comparison_results)")
            columns = [col[1] for col in cursor.fetchall()]
            
            if 'department' not in columns:
                cursor.execute("ALTER TABLE comparison_results ADD COLUMN department TEXT DEFAULT 'DEPARTMENT NOT SPECIFIED'")
                print("✅ Added department column to Bank Adviser comparison_results")
            else:
                print("[INFO] Bank Adviser comparison_results already has department column")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Error fixing Bank Adviser database: {e}")
        return False

def verify_all_fixes():
    """Verify that all fixes are working"""
    
    print("\n[VERIFY] VERIFYING ALL EMERGENCY FIXES")
    print("=" * 70)
    
    # Test Payroll Audit database
    db_path = 'payroll_audit.db'
    if os.path.exists(db_path):
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Test comparison_results
            cursor.execute("SELECT department FROM comparison_results LIMIT 1")
            result = cursor.fetchone()
            if result:
                print("✅ Payroll Audit comparison_results department column working")
            else:
                print("⚠️ Payroll Audit comparison_results has no data")
            
            # Test extracted_data
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='extracted_data'")
            if cursor.fetchone():
                cursor.execute("SELECT department FROM extracted_data LIMIT 1")
                result = cursor.fetchone()
                if result:
                    print("✅ Payroll Audit extracted_data department column working")
                else:
                    print("⚠️ Payroll Audit extracted_data has no data")
            
            # Test tracker_results
            cursor.execute("SELECT department FROM tracker_results LIMIT 1")
            result = cursor.fetchone()
            if result:
                print("✅ Payroll Audit tracker_results department column working")
            else:
                print("⚠️ Payroll Audit tracker_results has no data")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Payroll Audit database verification failed: {e}")
    
    # Test Bank Adviser database
    db_path = 'data/templar_payroll_auditor.db'
    if os.path.exists(db_path):
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Test tracker_results
            cursor.execute("SELECT department FROM tracker_results LIMIT 1")
            result = cursor.fetchone()
            if result:
                print("✅ Bank Adviser tracker_results department column working")
            else:
                print("⚠️ Bank Adviser tracker_results has no data")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Bank Adviser database verification failed: {e}")

def main():
    """Main emergency fix function"""
    
    print("[EMERGENCY FIX] CRITICAL DATABASE SCHEMA REPAIR")
    print("=" * 80)
    
    success = True
    
    # Step 1: Fix comparison_results department column
    if not fix_comparison_results_department_column():
        success = False
    
    # Step 2: Fix extracted_data department column
    if not fix_extracted_data_department_column():
        success = False
    
    # Step 3: Fix Bank Adviser database schema
    if not fix_bank_adviser_database_schema():
        success = False
    
    # Step 4: Test pre-reporting query
    if not test_pre_reporting_query():
        success = False
    
    # Step 5: Verify all fixes
    verify_all_fixes()
    
    if success:
        print("\n[SUCCESS] Emergency database schema fixes completed!")
        print("Results:")
        print("  ✅ Fixed comparison_results department column")
        print("  ✅ Fixed extracted_data department column")
        print("  ✅ Fixed tracker_results department column")
        print("  ✅ Pre-reporting queries should now work")
        print("  ✅ API calls should no longer fail")
        print("\nThe app should now work without 'no such column: department' errors!")
    else:
        print("\n[WARN] Some emergency fixes had issues")
    
    return success

if __name__ == "__main__":
    main()
