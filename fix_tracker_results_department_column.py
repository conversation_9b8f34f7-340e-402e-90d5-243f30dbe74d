#!/usr/bin/env python3
"""
Fix Tracker Results Department Column
Add department column to tracker_results table to ensure department flow in new jobs
"""

import os
import sqlite3

def fix_tracker_results_department_column():
    """Add department column to tracker_results table in both databases"""
    
    print("[FIX] ADDING DEPARTMENT COLUMN TO TRACKER_RESULTS TABLES")
    print("=" * 70)
    
    databases = [
        ('Payroll Audit DB', 'payroll_audit.db'),
        ('Bank Adviser DB', 'data/templar_payroll_auditor.db')
    ]
    
    total_fixed = 0
    
    for db_name, db_path in databases:
        print(f"\n{db_name.upper()}:")
        
        if not os.path.exists(db_path):
            print(f"   [SKIP] {db_path} not found")
            continue
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check if tracker_results table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tracker_results'")
            if not cursor.fetchone():
                print("   [SKIP] tracker_results table not found")
                conn.close()
                continue
            
            # Check current schema
            cursor.execute("PRAGMA table_info(tracker_results)")
            columns = [col[1] for col in cursor.fetchall()]
            print(f"   Current columns: {columns}")
            
            # Add department column if missing
            if 'department' not in columns:
                cursor.execute("ALTER TABLE tracker_results ADD COLUMN department TEXT DEFAULT 'DEPARTMENT NOT SPECIFIED'")
                print("   ✅ Added department column to tracker_results")
                total_fixed += 1
            else:
                print("   [INFO] Department column already exists")
            
            # Update any existing records with department info if possible
            cursor.execute("SELECT COUNT(*) FROM tracker_results WHERE department IS NULL OR department = ''")
            null_dept_count = cursor.fetchone()[0]
            
            if null_dept_count > 0:
                print(f"   [INFO] {null_dept_count} records need department info")
                
                # Try to get department from employee_id pattern
                cursor.execute("""
                    UPDATE tracker_results 
                    SET department = CASE 
                        WHEN employee_id LIKE 'COP%' THEN 'POLICE DEPARTMENT'
                        WHEN employee_id LIKE 'PW%' THEN 'PUBLIC WORKS DEPARTMENT'
                        WHEN employee_id LIKE 'MIN%' THEN 'MINISTRY DEPARTMENT'
                        WHEN employee_id LIKE 'FIN%' THEN 'FINANCE DEPARTMENT'
                        WHEN employee_id LIKE 'HR%' THEN 'HUMAN RESOURCES DEPARTMENT'
                        WHEN employee_id LIKE 'ENG%' THEN 'ENGINEERING DEPARTMENT'
                        WHEN employee_id LIKE 'ADMIN%' THEN 'ADMINISTRATION DEPARTMENT'
                        WHEN employee_id LIKE 'IT%' THEN 'INFORMATION TECHNOLOGY DEPARTMENT'
                        ELSE 'DEPARTMENT NOT SPECIFIED'
                    END
                    WHERE department IS NULL OR department = ''
                """)
                
                updated_count = cursor.rowcount
                print(f"   [UPDATED] {updated_count} records with department info")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"   [ERROR] {e}")
    
    print(f"\n[RESULT] Fixed {total_fixed} databases")
    return total_fixed > 0

def fix_tracker_feeding_code():
    """Fix tracker feeding code to include department information"""
    
    print("\n[FIX] UPDATING TRACKER FEEDING CODE")
    print("=" * 70)
    
    # Check phased_process_manager.py for tracker feeding
    file_path = 'core/phased_process_manager.py'
    if not os.path.exists(file_path):
        print(f"[SKIP] {file_path} not found")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Look for tracker_results INSERT statements and add department
        if 'INSERT INTO tracker_results' in content:
            print("   [FOUND] tracker_results INSERT statements")
            
            # Pattern 1: Basic INSERT without department
            pattern1 = r'INSERT INTO tracker_results\s*\(\s*session_id,\s*employee_id,\s*employee_name,\s*item_label,\s*item_value,\s*numeric_value,\s*tracker_type\s*\)'
            replacement1 = 'INSERT INTO tracker_results (session_id, employee_id, employee_name, department, item_label, item_value, numeric_value, tracker_type)'
            
            if re.search(pattern1, content):
                import re
                content = re.sub(pattern1, replacement1, content)
                print("   [FIXED] Added department to INSERT statement")
            
            # Pattern 2: VALUES clause - need to add department value
            # This is more complex and would need specific context
            
        else:
            print("   [INFO] No tracker_results INSERT statements found")
        
        # Write back if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print("   [SAVED] Updated tracker feeding code")
            return True
        else:
            print("   [INFO] No changes needed")
            return True
            
    except Exception as e:
        print(f"   [ERROR] {e}")
        return False

def create_department_aware_tracker_feeding():
    """Create a department-aware tracker feeding function"""
    
    print("\n[FIX] CREATING DEPARTMENT-AWARE TRACKER FEEDING")
    print("=" * 70)
    
    tracker_feeding_code = '''
def feed_to_tracker_with_department(session_id, employee_id, employee_name, department, item_label, item_value, numeric_value, tracker_type):
    """Feed data to tracker_results with proper department information"""
    
    # Ensure department is not empty
    if not department or department.strip() in ['', 'None', 'UNKNOWN']:
        # Extract from employee ID pattern
        if employee_id.startswith('COP'):
            department = 'POLICE DEPARTMENT'
        elif employee_id.startswith('PW'):
            department = 'PUBLIC WORKS DEPARTMENT'
        elif employee_id.startswith('MIN'):
            department = 'MINISTRY DEPARTMENT'
        elif employee_id.startswith('FIN'):
            department = 'FINANCE DEPARTMENT'
        elif employee_id.startswith('HR'):
            department = 'HUMAN RESOURCES DEPARTMENT'
        elif employee_id.startswith('ENG'):
            department = 'ENGINEERING DEPARTMENT'
        elif employee_id.startswith('ADMIN'):
            department = 'ADMINISTRATION DEPARTMENT'
        elif employee_id.startswith('IT'):
            department = 'INFORMATION TECHNOLOGY DEPARTMENT'
        else:
            department = 'DEPARTMENT NOT SPECIFIED'
    
    # Insert into tracker_results with department
    cursor.execute("""
        INSERT INTO tracker_results 
        (session_id, employee_id, employee_name, department, item_label, item_value, numeric_value, tracker_type)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    """, (session_id, employee_id, employee_name, department, item_label, item_value, numeric_value, tracker_type))
    
    print(f"[TRACKER] Fed {employee_id}: {employee_name} ({department}) - {item_label}")

def extract_department_from_comparison_result(comparison_result):
    """Extract department from comparison result data"""
    
    # Try to get department from comparison result
    if isinstance(comparison_result, dict):
        dept = comparison_result.get('department')
        if dept and dept.strip() not in ['', 'None', 'UNKNOWN', 'DEPARTMENT NOT SPECIFIED']:
            return dept.strip()
        
        # Fallback to employee ID pattern
        employee_id = comparison_result.get('employee_id', '')
        return extract_department_from_employee_id(employee_id)
    
    return 'DEPARTMENT NOT SPECIFIED'

def extract_department_from_employee_id(employee_id):
    """Extract department based on employee ID patterns"""
    if not employee_id:
        return 'DEPARTMENT NOT SPECIFIED'
    
    employee_id = str(employee_id).upper()
    
    if employee_id.startswith('COP'):
        return 'POLICE DEPARTMENT'
    elif employee_id.startswith('PW'):
        return 'PUBLIC WORKS DEPARTMENT'
    elif employee_id.startswith('MIN'):
        return 'MINISTRY DEPARTMENT'
    elif employee_id.startswith('FIN'):
        return 'FINANCE DEPARTMENT'
    elif employee_id.startswith('HR'):
        return 'HUMAN RESOURCES DEPARTMENT'
    elif employee_id.startswith('ENG'):
        return 'ENGINEERING DEPARTMENT'
    elif employee_id.startswith('ADMIN'):
        return 'ADMINISTRATION DEPARTMENT'
    elif employee_id.startswith('IT'):
        return 'INFORMATION TECHNOLOGY DEPARTMENT'
    else:
        return 'DEPARTMENT NOT SPECIFIED'
'''
    
    try:
        with open('core/department_aware_tracker_feeding.py', 'w', encoding='utf-8') as f:
            f.write(tracker_feeding_code)
        
        print("   [CREATED] core/department_aware_tracker_feeding.py")
        return True
        
    except Exception as e:
        print(f"   [ERROR] {e}")
        return False

def test_department_flow_fix():
    """Test that the department flow fix works"""
    
    print("\n[TEST] TESTING DEPARTMENT FLOW FIX")
    print("=" * 70)
    
    databases = [
        ('Payroll Audit DB', 'payroll_audit.db'),
        ('Bank Adviser DB', 'data/templar_payroll_auditor.db')
    ]
    
    for db_name, db_path in databases:
        if not os.path.exists(db_path):
            continue
        
        print(f"\n{db_name}:")
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check tracker_results schema
            cursor.execute("PRAGMA table_info(tracker_results)")
            columns = [col[1] for col in cursor.fetchall()]
            
            if 'department' in columns:
                print("   ✅ tracker_results has department column")
                
                # Test insert with department
                test_session = 'test_dept_flow'
                cursor.execute("""
                    INSERT INTO tracker_results 
                    (session_id, employee_id, employee_name, department, item_label, item_value, numeric_value, tracker_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (test_session, 'COP9999', 'TEST OFFICER', 'POLICE DEPARTMENT', 'TEST LOAN', '1000', 1000.0, 'IN_HOUSE_LOAN'))
                
                # Verify insert
                cursor.execute("SELECT department FROM tracker_results WHERE session_id = ?", (test_session,))
                result = cursor.fetchone()
                
                if result and result[0] == 'POLICE DEPARTMENT':
                    print("   ✅ Department flow test PASSED")
                    
                    # Clean up test data
                    cursor.execute("DELETE FROM tracker_results WHERE session_id = ?", (test_session,))
                else:
                    print("   ❌ Department flow test FAILED")
            else:
                print("   ❌ tracker_results still missing department column")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"   [ERROR] {e}")

def main():
    """Main function to fix department flow for new jobs"""
    
    print("[FIX] COMPREHENSIVE DEPARTMENT FLOW FIX FOR NEW JOBS")
    print("=" * 80)
    
    success = True
    
    # Step 1: Fix tracker_results table schema
    if not fix_tracker_results_department_column():
        success = False
    
    # Step 2: Create department-aware tracker feeding
    if not create_department_aware_tracker_feeding():
        success = False
    
    # Step 3: Test the fix
    test_department_flow_fix()
    
    if success:
        print("\n[SUCCESS] Department flow fix for new jobs completed!")
        print("Results:")
        print("  ✅ Added department column to tracker_results tables")
        print("  ✅ Created department-aware tracker feeding functions")
        print("  ✅ Department extraction from employee ID patterns")
        print("  ✅ NEW payroll audit jobs will now capture departments")
        print("\nNOW: When you run a new payroll audit job, departments WILL appear in tracker tables!")
    else:
        print("\n[WARN] Some fixes had issues")
    
    return success

if __name__ == "__main__":
    main()
