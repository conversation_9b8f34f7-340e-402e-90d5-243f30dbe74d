#!/usr/bin/env python3
"""
Check Department Data in Database
"""

import os
import sqlite3

def check_department_data():
    """Check department data availability"""
    
    print("[CHECK] EXAMINING DEPARTMENT DATA")
    print("=" * 50)
    
    # Find database
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("1. COMPARISON RESULTS TABLE:")
        cursor.execute("SELECT employee_id, employee_name FROM comparison_results LIMIT 5")
        comp_results = cursor.fetchall()
        for emp_id, emp_name in comp_results:
            print(f"   {emp_id}: {emp_name}")
        
        print("\n2. EXTRACTED DATA TABLE:")
        cursor.execute("SELECT COUNT(*) FROM extracted_data")
        count = cursor.fetchone()[0]
        print(f"   Rows: {count}")
        
        if count > 0:
            cursor.execute("SELECT employee_no, employee_name, department FROM extracted_data LIMIT 5")
            ext_results = cursor.fetchall()
            for emp_no, emp_name, dept in ext_results:
                print(f"   {emp_no}: {emp_name} - {dept}")
        
        print("\n3. CHECKING FOR DEPARTMENT COLUMN IN COMPARISON_RESULTS:")
        cursor.execute("PRAGMA table_info(comparison_results)")
        columns = cursor.fetchall()
        has_dept = any(col[1] == 'department' for col in columns)
        print(f"   Has department column: {has_dept}")
        
        if has_dept:
            cursor.execute("SELECT DISTINCT employee_id, employee_name FROM comparison_results WHERE department IS NOT NULL LIMIT 3")
            dept_results = cursor.fetchall()
            print(f"   Records with department: {len(dept_results)}")
        
        print("\n4. CHECKING TRACKER TABLES:")
        tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   {table}: {count} rows")
            
            if count > 0:
                cursor.execute(f"SELECT employee_no, employee_name, department FROM {table} LIMIT 2")
                tracker_results = cursor.fetchall()
                for emp_no, emp_name, dept in tracker_results:
                    print(f"     {emp_no}: {emp_name} - {dept}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error: {e}")
        return False

if __name__ == "__main__":
    check_department_data()
