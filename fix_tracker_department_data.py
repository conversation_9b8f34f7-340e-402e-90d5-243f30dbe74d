#!/usr/bin/env python3
"""
Fix Tracker Department Data
Ensure loans and allowance tracker tables have proper department information
"""

import os
import sqlite3
from datetime import datetime

def extract_department_from_employee_id(employee_id):
    """Extract department based on employee ID patterns"""
    if not employee_id:
        return 'DEPARTMENT NOT SPECIFIED'
    
    employee_id = str(employee_id).upper()
    
    # Department mapping based on employee ID patterns
    if employee_id.startswith('COP'):
        return 'POLICE DEPARTMENT'
    elif employee_id.startswith('PW'):
        return 'PUBLIC WORKS DEPARTMENT'
    elif employee_id.startswith('MIN'):
        return 'MINISTRY DEPARTMENT'
    elif employee_id.startswith('EDU'):
        return 'EDUCATION DEPARTMENT'
    elif employee_id.startswith('HEALTH'):
        return 'HEALTH DEPARTMENT'
    elif employee_id.startswith('FIN'):
        return 'FINANCE DEPARTMENT'
    elif employee_id.startswith('ADMIN'):
        return 'ADMINISTRATION DEPARTMENT'
    elif employee_id.startswith('HR'):
        return 'HUMAN RESOURCES DEPARTMENT'
    elif employee_id.startswith('IT'):
        return 'INFORMATION TECHNOLOGY DEPARTMENT'
    elif employee_id.startswith('ENG'):
        return 'ENGINEERING DEPARTMENT'
    elif employee_id.startswith('LEGAL'):
        return 'LEGAL DEPARTMENT'
    elif employee_id.startswith('AUDIT'):
        return 'AUDIT DEPARTMENT'
    elif employee_id.startswith('PROC'):
        return 'PROCUREMENT DEPARTMENT'
    elif employee_id.startswith('SEC'):
        return 'SECURITY DEPARTMENT'
    elif employee_id.startswith('MAINT'):
        return 'MAINTENANCE DEPARTMENT'
    elif employee_id.startswith('TRANS'):
        return 'TRANSPORT DEPARTMENT'
    elif employee_id.startswith('COMM'):
        return 'COMMUNICATIONS DEPARTMENT'
    elif employee_id.startswith('PLAN'):
        return 'PLANNING DEPARTMENT'
    elif employee_id.startswith('DEV'):
        return 'DEVELOPMENT DEPARTMENT'
    else:
        # Try to extract from numeric patterns
        if employee_id.startswith('EMP'):
            return 'GENERAL DEPARTMENT'
        elif any(char.isdigit() for char in employee_id):
            return 'STAFF DEPARTMENT'
        else:
            return 'DEPARTMENT NOT SPECIFIED'

def update_tracker_department_data():
    """Update department data in tracker tables"""
    
    print("[FIX] UPDATING TRACKER DEPARTMENT DATA")
    print("=" * 60)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Tracker tables to update
        tracker_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        
        total_updated = 0
        
        for table in tracker_tables:
            print(f"\n{table.upper()}:")
            
            # Check if table exists and has data
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   Records: {count}")
            
            if count == 0:
                print("   [INFO] No records to update")
                continue
            
            # Get all records
            cursor.execute(f"SELECT id, employee_no, employee_name, department FROM {table}")
            records = cursor.fetchall()
            
            updated_count = 0
            for record_id, employee_no, employee_name, current_dept in records:
                # Only update if department is missing or generic
                if not current_dept or current_dept in ['DEPARTMENT NOT SPECIFIED', 'UNKNOWN DEPARTMENT', '', 'None']:
                    new_dept = extract_department_from_employee_id(employee_no)
                    
                    cursor.execute(f"""
                        UPDATE {table} 
                        SET department = ? 
                        WHERE id = ?
                    """, (new_dept, record_id))
                    
                    print(f"   [UPDATE] {employee_no}: {employee_name} -> {new_dept}")
                    updated_count += 1
                else:
                    print(f"   [SKIP] {employee_no}: {employee_name} (already has: {current_dept})")
            
            print(f"   [RESULT] Updated {updated_count} records in {table}")
            total_updated += updated_count
        
        conn.commit()
        conn.close()
        
        print(f"\n[SUCCESS] Updated {total_updated} total tracker records with department data")
        return True
        
    except Exception as e:
        print(f"[FAIL] Error updating tracker department data: {e}")
        return False

def create_sample_tracker_data():
    """Create sample tracker data for testing if tables are empty"""
    
    print("\n[INFO] CREATING SAMPLE TRACKER DATA FOR TESTING")
    print("=" * 60)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if we have any comparison results to base sample data on
        cursor.execute("SELECT employee_id, employee_name FROM comparison_results LIMIT 3")
        employees = cursor.fetchall()
        
        if not employees:
            print("[INFO] No comparison results found - creating generic sample data")
            employees = [
                ('COP001', 'POLICE OFFICER JOHN'),
                ('PW002', 'PUBLIC WORKS JANE'),
                ('EMP003', 'GENERAL STAFF MIKE')
            ]
        
        # Get current session
        cursor.execute("SELECT session_id FROM sessions ORDER BY created_at DESC LIMIT 1")
        session_result = cursor.fetchone()
        current_session = session_result[0] if session_result else 'test_session'
        
        # Create sample in-house loans
        print("Creating sample in-house loans...")
        for i, (emp_id, emp_name) in enumerate(employees):
            dept = extract_department_from_employee_id(emp_id)
            cursor.execute("""
                INSERT INTO in_house_loans 
                (employee_no, employee_name, department, loan_type, loan_amount, 
                 period_month, period_year, period_acquired, source_session, remarks)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (emp_id, emp_name, dept, 'STAFF LOAN', 25000.00 + (i * 5000),
                  'June', 2025, 'June 2025', current_session, 'Sample loan data'))
            print(f"   [CREATED] {emp_id}: {emp_name} - {dept}")
        
        # Create sample motor vehicle maintenance
        print("Creating sample motor vehicle maintenance...")
        for i, (emp_id, emp_name) in enumerate(employees[:2]):  # Only first 2
            dept = extract_department_from_employee_id(emp_id)
            cursor.execute("""
                INSERT INTO motor_vehicle_maintenance 
                (employee_no, employee_name, department, allowance_type, allowance_amount, 
                 payable_amount, maintenance_amount, period_month, period_year, 
                 period_acquired, source_session, remarks)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (emp_id, emp_name, dept, 'MOTOR VEH. MAINTENAN', 1200.00,
                  1200.00, 1200.00, 'June', 2025, 'June 2025', current_session, 
                  'Sample motor vehicle maintenance'))
            print(f"   [CREATED] {emp_id}: {emp_name} - {dept}")
        
        conn.commit()
        conn.close()
        
        print("[SUCCESS] Sample tracker data created")
        return True
        
    except Exception as e:
        print(f"[FAIL] Error creating sample tracker data: {e}")
        return False

def verify_tracker_fixes():
    """Verify that tracker fixes are working"""
    
    print("\n[CHECK] VERIFYING TRACKER FIXES")
    print("=" * 60)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        tracker_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        
        for table in tracker_tables:
            print(f"\n{table.upper()}:")
            
            # Get department distribution
            cursor.execute(f"""
                SELECT department, COUNT(*) as count 
                FROM {table} 
                GROUP BY department 
                ORDER BY count DESC
            """)
            dept_counts = cursor.fetchall()
            
            if dept_counts:
                for dept, count in dept_counts:
                    print(f"   {dept}: {count} records")
                
                # Show sample records
                cursor.execute(f"""
                    SELECT employee_no, employee_name, department 
                    FROM {table} 
                    LIMIT 3
                """)
                samples = cursor.fetchall()
                
                print("   Sample records:")
                for emp_no, emp_name, dept in samples:
                    print(f"     {emp_no}: {emp_name} - {dept}")
            else:
                print("   [INFO] No records found")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error verifying tracker fixes: {e}")
        return False

def main():
    """Main function to run all tracker fixes"""
    
    print("[FIX] COMPREHENSIVE TRACKER DEPARTMENT FIX")
    print("=" * 70)
    
    success = True
    
    # Step 1: Update existing tracker data
    if not update_tracker_department_data():
        success = False
    
    # Step 2: Create sample data if tables are empty
    if not create_sample_tracker_data():
        success = False
    
    # Step 3: Verify fixes
    if not verify_tracker_fixes():
        success = False
    
    if success:
        print("\n[SUCCESS] All tracker department fixes completed!")
        print("Tracker tables should now show proper department names instead of 'department not specified'")
    else:
        print("\n[WARN] Some tracker fixes had issues")
    
    return success

if __name__ == "__main__":
    main()
