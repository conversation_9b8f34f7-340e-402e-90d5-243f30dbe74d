#!/usr/bin/env python3
"""
Python Process Validator
Validates that Python environment is working correctly
"""

import sys
import os
import json
from datetime import datetime

def validate_python_environment():
    """Validate Python environment and dependencies"""
    
    validation_result = {
        'success': True,
        'python_version': sys.version,
        'python_executable': sys.executable,
        'working_directory': os.getcwd(),
        'timestamp': datetime.now().isoformat(),
        'issues': []
    }
    
    try:
        # Test basic imports
        import sqlite3
        validation_result['sqlite3_available'] = True
    except ImportError as e:
        validation_result['sqlite3_available'] = False
        validation_result['issues'].append(f'SQLite3 not available: {e}')
        validation_result['success'] = False
    
    try:
        import json
        validation_result['json_available'] = True
    except ImportError as e:
        validation_result['json_available'] = False
        validation_result['issues'].append(f'JSON not available: {e}')
        validation_result['success'] = False
    
    # Test file system access
    try:
        test_file = 'test_write_access.tmp'
        with open(test_file, 'w') as f:
            f.write('test')
        os.remove(test_file)
        validation_result['file_system_access'] = True
    except Exception as e:
        validation_result['file_system_access'] = False
        validation_result['issues'].append(f'File system access issue: {e}')
        validation_result['success'] = False
    
    # Test database access
    try:
        if os.path.exists('payroll_audit.db'):
            import sqlite3
            conn = sqlite3.connect('payroll_audit.db')
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM sqlite_master')
            table_count = cursor.fetchone()[0]
            conn.close()
            validation_result['database_access'] = True
            validation_result['database_tables'] = table_count
        else:
            validation_result['database_access'] = False
            validation_result['issues'].append('Database file not found')
    except Exception as e:
        validation_result['database_access'] = False
        validation_result['issues'].append(f'Database access issue: {e}')
    
    return validation_result

def main():
    """Main validation function"""
    
    if len(sys.argv) > 1 and sys.argv[1] == 'validate':
        result = validate_python_environment()
        print(json.dumps(result, indent=2))
        
        # Exit with appropriate code
        sys.exit(0 if result['success'] else 1)
    else:
        print("Python Process Validator")
        print("Usage: python python_process_validator.py validate")
        sys.exit(0)

if __name__ == "__main__":
    main()
