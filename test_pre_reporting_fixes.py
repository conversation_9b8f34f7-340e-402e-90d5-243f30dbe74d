#!/usr/bin/env python3
"""
Test Pre-Reporting Fixes
Comprehensive test script to verify all 8 pre-reporting issues have been resolved.
"""

import os
import sys
import sqlite3
import json
from datetime import datetime

def test_pre_reporting_fixes():
    """Test all pre-reporting fixes"""
    
    print("🧪 TESTING PRE-REPORTING FIXES")
    print("=" * 60)
    
    # Database path
    db_path = os.path.join(os.path.dirname(__file__), 'payroll_auditor.db')
    
    if not os.path.exists(db_path):
        print("❌ Database not found. Please run a payroll audit first.")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        test_results = {}
        
        print("\n1. 🔍 TESTING SORTING FEATURE:")
        # Check if the sorting functionality has been implemented
        # This is mainly a frontend feature, so we'll check for related data structures
        cursor.execute("SELECT COUNT(*) FROM comparison_results")
        comparison_count = cursor.fetchone()[0]
        
        if comparison_count > 0:
            print(f"   ✅ Found {comparison_count} comparison results for sorting")
            test_results['sorting_feature'] = True
        else:
            print("   ⚠️ No comparison results found for sorting test")
            test_results['sorting_feature'] = False
        
        print("\n2. 📅 TESTING MONTH NAME DISPLAY:")
        # Check if session has month information
        cursor.execute("""
            SELECT current_month, current_year, previous_month, previous_year
            FROM audit_sessions 
            ORDER BY created_at DESC LIMIT 1
        """)
        
        session_info = cursor.fetchone()
        if session_info and all(session_info):
            current_month, current_year, previous_month, previous_year = session_info
            print(f"   ✅ Session has month data: {previous_month} {previous_year} → {current_month} {current_year}")
            test_results['month_names'] = True
        else:
            print("   ⚠️ No session month data found")
            test_results['month_names'] = False
        
        print("\n3. 🏢 TESTING DEPARTMENT FIELD:")
        # Check if employees have department information
        cursor.execute("""
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN department IS NOT NULL AND department != '' THEN 1 END) as with_dept
            FROM employees
        """)
        
        dept_info = cursor.fetchone()
        if dept_info:
            total, with_dept = dept_info
            dept_percentage = (with_dept / total * 100) if total > 0 else 0
            print(f"   📊 Department coverage: {with_dept}/{total} employees ({dept_percentage:.1f}%)")
            test_results['department_field'] = dept_percentage > 50
        else:
            print("   ⚠️ No employee data found")
            test_results['department_field'] = False
        
        print("\n4. 🔢 TESTING NUMERIC FORMATTING:")
        # Check if comparison results have proper numeric formatting
        cursor.execute("""
            SELECT item_name, previous_value, current_value, difference
            FROM comparison_results 
            WHERE previous_value IS NOT NULL AND current_value IS NOT NULL
            LIMIT 5
        """)
        
        numeric_samples = cursor.fetchall()
        if numeric_samples:
            print("   📊 Sample numeric values:")
            for item_name, prev_val, curr_val, diff in numeric_samples:
                print(f"      • {item_name}: {prev_val} → {curr_val} (diff: {diff})")
            test_results['numeric_formatting'] = True
        else:
            print("   ⚠️ No numeric comparison data found")
            test_results['numeric_formatting'] = False
        
        print("\n5. 📋 TESTING SECTION CLASSIFICATION:")
        # Check for Educational Subsidy classification
        cursor.execute("""
            SELECT section, COUNT(*) as count
            FROM comparison_results 
            WHERE UPPER(item_name) LIKE '%EDUCATIONAL%SUBSIDY%'
            GROUP BY section
        """)
        
        educational_subsidy_sections = cursor.fetchall()
        if educational_subsidy_sections:
            print("   📋 Educational Subsidy classification:")
            earnings_count = 0
            for section, count in educational_subsidy_sections:
                status = "✅" if section.upper() == "EARNINGS" else "⚠️"
                print(f"      {status} {section}: {count} items")
                if section.upper() == "EARNINGS":
                    earnings_count += count
            
            test_results['section_classification'] = earnings_count > 0
        else:
            print("   ℹ️ No Educational Subsidy items found")
            test_results['section_classification'] = True  # No issues if no items
        
        print("\n6. 👤 TESTING GHANA CARD ID MISCLASSIFICATION:")
        # Check for Ghana Card ID in employee names
        cursor.execute("""
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN employee_name LIKE '%Ghana Card%' 
                              OR employee_name LIKE 'GHA-%' 
                              OR employee_name = 'Ghana Card ID' THEN 1 END) as ghana_card_issues
            FROM employees
        """)
        
        ghana_card_info = cursor.fetchone()
        if ghana_card_info:
            total, issues = ghana_card_info
            print(f"   👤 Ghana Card ID issues: {issues}/{total} employees")
            test_results['ghana_card_fix'] = issues == 0
        else:
            print("   ⚠️ No employee data found")
            test_results['ghana_card_fix'] = False
        
        print("\n7. 🏷️ TESTING SECTION NAME AS EMPLOYEE NAME:")
        # Check for section names as employee names
        section_names = ['PERSONAL DETAILS', 'EARNINGS', 'DEDUCTIONS', 'LOANS', 'EMPLOYERS CONTRIBUTION']
        section_issues = 0
        
        for section_name in section_names:
            cursor.execute("SELECT COUNT(*) FROM employees WHERE employee_name = ?", (section_name,))
            count = cursor.fetchone()[0]
            if count > 0:
                print(f"   ⚠️ Found {count} employees with '{section_name}' as name")
                section_issues += count
        
        if section_issues == 0:
            print("   ✅ No section names found as employee names")
        
        test_results['section_name_fix'] = section_issues == 0
        
        print("\n8. 📄 TESTING REPORT MANAGER INTEGRATION:")
        # Check if reports table exists and has recent entries
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='reports'
        """)
        
        if cursor.fetchone():
            cursor.execute("""
                SELECT COUNT(*) as total,
                       COUNT(CASE WHEN created_at > datetime('now', '-1 day') THEN 1 END) as recent
                FROM reports
                WHERE type IN ('word', 'pdf', 'excel')
            """)
            
            report_info = cursor.fetchone()
            if report_info:
                total, recent = report_info
                print(f"   📄 Report Manager: {total} total reports, {recent} recent")
                test_results['report_manager'] = total > 0
            else:
                print("   ⚠️ No reports found in Report Manager")
                test_results['report_manager'] = False
        else:
            print("   ⚠️ Reports table not found")
            test_results['report_manager'] = False
        
        # Summary
        print(f"\n✅ TEST RESULTS SUMMARY:")
        print("=" * 40)
        
        passed_tests = sum(test_results.values())
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {status} {test_name.replace('_', ' ').title()}")
        
        print(f"\n📊 Overall Score: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("🎉 ALL TESTS PASSED! Pre-reporting fixes are working correctly.")
        else:
            print(f"⚠️ {total_tests - passed_tests} tests failed. Some issues may need attention.")
        
        conn.close()
        return passed_tests == total_tests
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_all_fixes():
    """Run all fix scripts before testing"""
    print("🔧 RUNNING ALL FIX SCRIPTS FIRST...")
    
    # Run section misclassification fix
    try:
        import subprocess
        result = subprocess.run([sys.executable, 'fix_section_misclassification.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Section misclassification fix completed")
        else:
            print(f"⚠️ Section misclassification fix had issues: {result.stderr}")
    except Exception as e:
        print(f"⚠️ Could not run section misclassification fix: {e}")
    
    # Run Ghana Card ID fix
    try:
        result = subprocess.run([sys.executable, 'fix_ghana_card_misclassification.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Ghana Card ID misclassification fix completed")
        else:
            print(f"⚠️ Ghana Card ID fix had issues: {result.stderr}")
    except Exception as e:
        print(f"⚠️ Could not run Ghana Card ID fix: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--run-fixes':
        run_all_fixes()
        print("\n" + "="*60 + "\n")
    
    success = test_pre_reporting_fixes()
    sys.exit(0 if success else 1)
