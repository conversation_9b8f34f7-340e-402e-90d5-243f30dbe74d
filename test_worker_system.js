#!/usr/bin/env node
/**
 * Test Worker System
 * Simple test to verify the worker thread system is working
 */

const { createPythonWorker } = require('./python-worker');
const path = require('path');

async function testWorkerSystem() {
  console.log('[TEST] Testing worker thread system...');
  
  try {
    // Test with our simple test script
    const result = await createPythonWorker(
      path.join(__dirname, 'test_worker_communication.py'),
      [],
      {
        timeout: 10000,
        enableProgressUpdates: true,
        enableRealtimeUpdates: true
      }
    );
    
    console.log('[TEST] ✅ Worker system working!');
    console.log('[TEST] Result length:', result.length);
    console.log('[TEST] First 200 chars:', result.substring(0, 200));
    
    // Check for expected messages
    if (result.includes('PROGRESS_UPDATE:')) {
      console.log('[TEST] ✅ Progress updates found');
    } else {
      console.log('[TEST] ❌ No progress updates found');
    }
    
    if (result.includes('REALTIME_UPDATE:')) {
      console.log('[TEST] ✅ Real-time updates found');
    } else {
      console.log('[TEST] ❌ No real-time updates found');
    }
    
    if (result.includes('SUCCESS:')) {
      console.log('[TEST] ✅ Success message found');
    } else {
      console.log('[TEST] ❌ No success message found');
    }
    
  } catch (error) {
    console.error('[TEST] ❌ Worker system failed:', error.message);
    console.error('[TEST] Full error:', error);
  }
}

// Run the test
testWorkerSystem().then(() => {
  console.log('[TEST] Test completed');
  process.exit(0);
}).catch((error) => {
  console.error('[TEST] Test failed:', error);
  process.exit(1);
});
