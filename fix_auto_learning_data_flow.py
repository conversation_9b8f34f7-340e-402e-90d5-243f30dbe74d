#!/usr/bin/env python3
"""
Fix Auto Learning Data Flow
Comprehensive fix for Auto Learning not receiving data for pending approval
"""

import os
import sqlite3
import json
from datetime import datetime

def create_auto_learning_tables():
    """Create or update Auto Learning tables"""
    
    print("[FIX] CREATING AUTO LEARNING TABLES")
    print("=" * 50)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create auto_learning_sessions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS auto_learning_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE NOT NULL,
                session_name TEXT,
                is_active BOOLEAN DEFAULT 1,
                started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                ended_at DATETIME,
                items_discovered INTEGER DEFAULT 0,
                items_approved INTEGER DEFAULT 0,
                items_rejected INTEGER DEFAULT 0
            )
        """)
        
        # Create auto_learning_results table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS auto_learning_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                section_name TEXT NOT NULL,
                item_label TEXT NOT NULL,
                item_value TEXT,
                confidence_score REAL NOT NULL,
                auto_approved BOOLEAN DEFAULT 0,
                dictionary_updated BOOLEAN DEFAULT 0,
                status TEXT DEFAULT 'pending',
                source TEXT DEFAULT 'extraction',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                processed_at DATETIME
            )
        """)
        
        # Create pending_items table for manual review
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS pending_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                section_name TEXT NOT NULL,
                item_label TEXT NOT NULL,
                item_value TEXT,
                confidence_score REAL NOT NULL,
                suggested_standard_name TEXT,
                requires_review BOOLEAN DEFAULT 1,
                status TEXT DEFAULT 'pending',
                approved_by TEXT,
                approved_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        print("[OK] Auto Learning tables created/verified")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error creating Auto Learning tables: {e}")
        return False

def feed_comparison_results_to_auto_learning():
    """Feed comparison results to Auto Learning system"""
    
    print("\n[FIX] FEEDING COMPARISON RESULTS TO AUTO LEARNING")
    print("=" * 50)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute("SELECT session_id FROM sessions ORDER BY created_at DESC LIMIT 1")
        session_result = cursor.fetchone()
        if not session_result:
            print("[FAIL] No session found")
            return False
        
        current_session = session_result[0]
        
        # Create auto learning session
        auto_learning_session_id = f"auto_learning_{current_session}"
        cursor.execute("""
            INSERT OR REPLACE INTO auto_learning_sessions 
            (session_id, session_name, is_active, started_at)
            VALUES (?, ?, 1, ?)
        """, (auto_learning_session_id, f"Auto Learning for {current_session}", datetime.now().isoformat()))
        
        # Get comparison results
        cursor.execute("""
            SELECT DISTINCT section, item_label, current_value, change_type
            FROM comparison_results 
            WHERE session_id = ?
        """, (current_session,))
        
        comparison_results = cursor.fetchall()
        
        if not comparison_results:
            print("[FAIL] No comparison results found")
            return False
        
        print(f"[INFO] Processing {len(comparison_results)} comparison results")
        
        # Process each result for auto learning
        items_discovered = 0
        items_pending = 0
        
        for section, item_label, current_value, change_type in comparison_results:
            # Check if this is a new item (not in existing dictionary)
            is_new_item = True  # For now, treat all as potentially new
            
            # Calculate confidence score based on change type
            confidence_score = 1.0 if change_type == 'NEW' else 0.8
            
            # Auto-approve high confidence items
            auto_approved = confidence_score >= 1.0
            
            # Insert into auto_learning_results
            cursor.execute("""
                INSERT INTO auto_learning_results 
                (session_id, section_name, item_label, item_value, confidence_score, 
                 auto_approved, status, source)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (auto_learning_session_id, section, item_label, current_value, 
                  confidence_score, auto_approved, 
                  'approved' if auto_approved else 'pending', 'comparison_results'))
            
            items_discovered += 1
            
            # If not auto-approved, add to pending items for manual review
            if not auto_approved:
                suggested_name = item_label.replace('_', ' ').title()
                
                cursor.execute("""
                    INSERT INTO pending_items 
                    (session_id, section_name, item_label, item_value, confidence_score,
                     suggested_standard_name, requires_review, status)
                    VALUES (?, ?, ?, ?, ?, ?, 1, 'pending')
                """, (auto_learning_session_id, section, item_label, current_value,
                      confidence_score, suggested_name))
                
                items_pending += 1
        
        # Update session statistics
        cursor.execute("""
            UPDATE auto_learning_sessions 
            SET items_discovered = ?, items_approved = ?
            WHERE session_id = ?
        """, (items_discovered, items_discovered - items_pending, auto_learning_session_id))
        
        conn.commit()
        conn.close()
        
        print(f"[OK] Auto Learning data fed successfully:")
        print(f"   Items discovered: {items_discovered}")
        print(f"   Items auto-approved: {items_discovered - items_pending}")
        print(f"   Items pending review: {items_pending}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Error feeding data to Auto Learning: {e}")
        return False

def create_sample_pending_items():
    """Create sample pending items for testing"""
    
    print("\n[FIX] CREATING SAMPLE PENDING ITEMS")
    print("=" * 50)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute("SELECT session_id FROM sessions ORDER BY created_at DESC LIMIT 1")
        session_result = cursor.fetchone()
        if not session_result:
            print("[FAIL] No session found")
            return False
        
        current_session = session_result[0]
        auto_learning_session_id = f"auto_learning_{current_session}"
        
        # Sample items that need manual approval
        sample_items = [
            {
                'section': 'EARNINGS',
                'label': 'SPECIAL_ALLOWANCE',
                'value': '500.00',
                'confidence': 0.7,
                'suggested_name': 'Special Allowance'
            },
            {
                'section': 'DEDUCTIONS',
                'label': 'UNION_DUES',
                'value': '25.00',
                'confidence': 0.8,
                'suggested_name': 'Union Dues'
            },
            {
                'section': 'LOANS',
                'label': 'EMERGENCY_LOAN',
                'value': '1000.00',
                'confidence': 0.6,
                'suggested_name': 'Emergency Loan'
            }
        ]
        
        for item in sample_items:
            # Add to auto_learning_results
            cursor.execute("""
                INSERT INTO auto_learning_results 
                (session_id, section_name, item_label, item_value, confidence_score, 
                 auto_approved, status, source)
                VALUES (?, ?, ?, ?, ?, 0, 'pending', 'sample_data')
            """, (auto_learning_session_id, item['section'], item['label'], 
                  item['value'], item['confidence']))
            
            # Add to pending_items
            cursor.execute("""
                INSERT INTO pending_items 
                (session_id, section_name, item_label, item_value, confidence_score,
                 suggested_standard_name, requires_review, status)
                VALUES (?, ?, ?, ?, ?, ?, 1, 'pending')
            """, (auto_learning_session_id, item['section'], item['label'], 
                  item['value'], item['confidence'], item['suggested_name']))
            
            print(f"   [CREATED] {item['section']}: {item['label']} -> {item['suggested_name']}")
        
        conn.commit()
        conn.close()
        
        print(f"[OK] Created {len(sample_items)} sample pending items")
        return True
        
    except Exception as e:
        print(f"[FAIL] Error creating sample pending items: {e}")
        return False

def verify_auto_learning_fixes():
    """Verify that Auto Learning fixes are working"""
    
    print("\n[CHECK] VERIFYING AUTO LEARNING FIXES")
    print("=" * 50)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check auto learning sessions
        cursor.execute("SELECT COUNT(*) FROM auto_learning_sessions")
        session_count = cursor.fetchone()[0]
        print(f"[INFO] Auto Learning sessions: {session_count}")
        
        if session_count > 0:
            cursor.execute("""
                SELECT session_id, session_name, items_discovered, items_approved 
                FROM auto_learning_sessions 
                ORDER BY started_at DESC LIMIT 3
            """)
            sessions = cursor.fetchall()
            print("   Recent sessions:")
            for session_id, session_name, discovered, approved in sessions:
                print(f"     - {session_name}: {discovered} discovered, {approved} approved")
        
        # Check auto learning results
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results")
        results_count = cursor.fetchone()[0]
        print(f"[INFO] Auto Learning results: {results_count}")
        
        # Check pending items
        cursor.execute("SELECT COUNT(*) FROM pending_items WHERE status = 'pending'")
        pending_count = cursor.fetchone()[0]
        print(f"[INFO] Pending items for review: {pending_count}")
        
        if pending_count > 0:
            cursor.execute("""
                SELECT section_name, item_label, suggested_standard_name, confidence_score
                FROM pending_items 
                WHERE status = 'pending'
                ORDER BY confidence_score DESC
                LIMIT 5
            """)
            pending_items = cursor.fetchall()
            print("   Sample pending items:")
            for section, label, suggested, confidence in pending_items:
                print(f"     - {section}: {label} -> {suggested} (confidence: {confidence:.1f})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error verifying Auto Learning fixes: {e}")
        return False

def main():
    """Main function to run all Auto Learning fixes"""
    
    print("[FIX] COMPREHENSIVE AUTO LEARNING DATA FLOW FIX")
    print("=" * 70)
    
    success = True
    
    # Step 1: Create Auto Learning tables
    if not create_auto_learning_tables():
        success = False
    
    # Step 2: Feed comparison results to Auto Learning
    if not feed_comparison_results_to_auto_learning():
        success = False
    
    # Step 3: Create sample pending items
    if not create_sample_pending_items():
        success = False
    
    # Step 4: Verify fixes
    if not verify_auto_learning_fixes():
        success = False
    
    if success:
        print("\n[SUCCESS] All Auto Learning fixes completed!")
        print("Auto Learning should now:")
        print("  - Receive data from comparison results")
        print("  - Have pending items for manual approval")
        print("  - Have auto-approved high confidence items")
        print("  - Store all data in proper database tables")
    else:
        print("\n[WARN] Some Auto Learning fixes had issues")
    
    return success

if __name__ == "__main__":
    main()
