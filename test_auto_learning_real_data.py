#!/usr/bin/env python3
"""
Test Auto Learning with Real Data
Check if Auto Learning system is working with actual payroll data
"""

import os
import sqlite3
from datetime import datetime

def analyze_auto_learning_data():
    """Analyze the current state of Auto Learning data"""
    
    print("[TEST] AUTO LEARNING REAL DATA ANALYSIS")
    print("=" * 60)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check auto learning sessions
        print("1. AUTO LEARNING SESSIONS:")
        cursor.execute("SELECT COUNT(*) FROM auto_learning_sessions")
        session_count = cursor.fetchone()[0]
        print(f"   Total sessions: {session_count}")
        
        if session_count > 0:
            cursor.execute("""
                SELECT session_id, session_name, items_discovered, items_approved, started_at
                FROM auto_learning_sessions 
                ORDER BY started_at DESC
            """)
            sessions = cursor.fetchall()
            print("   Recent sessions:")
            for session_id, name, discovered, approved, started_at in sessions:
                print(f"     - {name}: {discovered} discovered, {approved} approved ({started_at})")
        
        # 2. Check auto learning results
        print("\n2. AUTO LEARNING RESULTS:")
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results")
        results_count = cursor.fetchone()[0]
        print(f"   Total results: {results_count}")
        
        if results_count > 0:
            # Check data sources
            cursor.execute("SELECT source, COUNT(*) FROM auto_learning_results GROUP BY source")
            sources = cursor.fetchall()
            print("   Data sources:")
            for source, count in sources:
                print(f"     - {source}: {count} items")
            
            # Check sections
            cursor.execute("SELECT section_name, COUNT(*) FROM auto_learning_results GROUP BY section_name")
            sections = cursor.fetchall()
            print("   Sections:")
            for section, count in sections:
                print(f"     - {section}: {count} items")
        
        # 3. Check pending items
        print("\n3. PENDING ITEMS FOR REVIEW:")
        cursor.execute("SELECT COUNT(*) FROM pending_items WHERE status = 'pending'")
        pending_count = cursor.fetchone()[0]
        print(f"   Pending items: {pending_count}")
        
        if pending_count > 0:
            cursor.execute("""
                SELECT section_name, item_label, confidence_score, suggested_standard_name
                FROM pending_items 
                WHERE status = 'pending'
                ORDER BY confidence_score DESC
                LIMIT 5
            """)
            pending_items = cursor.fetchall()
            print("   Sample pending items:")
            for section, label, confidence, suggested in pending_items:
                print(f"     - {section}: {label} -> {suggested} (confidence: {confidence})")
        
        # 4. Check comparison results (source data)
        print("\n4. SOURCE DATA (COMPARISON RESULTS):")
        cursor.execute("SELECT COUNT(*) FROM comparison_results")
        comparison_count = cursor.fetchone()[0]
        print(f"   Comparison results: {comparison_count}")
        
        if comparison_count > 0:
            cursor.execute("SELECT DISTINCT change_type FROM comparison_results")
            change_types = cursor.fetchall()
            print("   Change types:")
            for change_type, in change_types:
                cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE change_type = ?", (change_type,))
                count = cursor.fetchone()[0]
                print(f"     - {change_type}: {count} items")
        
        # 5. Check if Auto Learning is getting real data
        print("\n5. DATA FLOW ANALYSIS:")
        
        # Check if auto learning results match comparison results
        cursor.execute("""
            SELECT COUNT(*) FROM auto_learning_results al
            JOIN comparison_results cr ON 
                al.section_name = cr.section AND 
                al.item_label = cr.item_label
        """)
        matched_count = cursor.fetchone()[0]
        print(f"   Auto Learning items matching comparison results: {matched_count}")
        
        # Check for real vs sample data
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE source = 'comparison_results'")
        real_data_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE source = 'sample_data'")
        sample_data_count = cursor.fetchone()[0]
        
        print(f"   Real data items: {real_data_count}")
        print(f"   Sample data items: {sample_data_count}")
        
        conn.close()
        
        # 6. Assessment
        print("\n6. ASSESSMENT:")
        if real_data_count > 0:
            print("   ✅ Auto Learning is receiving real data from comparison results")
        else:
            print("   ❌ Auto Learning is only using sample data")
        
        if pending_count > 0:
            print("   ✅ Auto Learning has items pending for manual review")
        else:
            print("   ⚠️ No items pending for manual review")
        
        if matched_count > 0:
            print("   ✅ Auto Learning data matches comparison results")
        else:
            print("   ❌ Auto Learning data doesn't match comparison results")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Error analyzing Auto Learning data: {e}")
        return False

def test_auto_learning_with_real_audit():
    """Test Auto Learning by running a real audit process"""
    
    print("\n[TEST] TESTING AUTO LEARNING WITH REAL AUDIT PROCESS")
    print("=" * 60)
    
    try:
        # Check if we have PDF files for testing
        test_files = ['JUNE001.pdf', 'MAY001.pdf']
        available_files = [f for f in test_files if os.path.exists(f)]
        
        if len(available_files) < 2:
            print("[SKIP] Not enough test PDF files available for real audit test")
            print(f"   Available: {available_files}")
            print("   Need: 2 PDF files for current and previous month")
            return False
        
        print(f"[INFO] Found test files: {available_files}")
        
        # Run a test audit process to generate real data
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager(debug_mode=True)
        
        # Test the auto learning phase specifically
        print("   Testing Auto Learning phase...")
        
        # This would normally be called after comparison phase
        result = manager.execute_auto_learning_phase()
        
        if result.get('success'):
            print("   ✅ Auto Learning phase executed successfully")
            print(f"   Items processed: {result.get('items_processed', 0)}")
            print(f"   Items auto-approved: {result.get('items_auto_approved', 0)}")
            print(f"   Items pending review: {result.get('items_pending_review', 0)}")
            return True
        else:
            print(f"   ❌ Auto Learning phase failed: {result.get('error', 'Unknown error')}")
            return False
            
    except ImportError:
        print("[SKIP] PhasedProcessManager not available for testing")
        return False
    except Exception as e:
        print(f"[FAIL] Error testing Auto Learning with real audit: {e}")
        return False

def main():
    """Main function to test Auto Learning with real data"""
    
    print("[TEST] AUTO LEARNING REAL DATA VALIDATION")
    print("=" * 70)
    
    # Step 1: Analyze current Auto Learning data
    analysis_success = analyze_auto_learning_data()
    
    # Step 2: Test with real audit process (if possible)
    test_success = test_auto_learning_with_real_audit()
    
    # Summary
    print("\n[SUMMARY] AUTO LEARNING REAL DATA TEST RESULTS")
    print("=" * 70)
    
    if analysis_success:
        print("✅ Auto Learning data analysis completed")
    else:
        print("❌ Auto Learning data analysis failed")
    
    if test_success:
        print("✅ Auto Learning tested with real audit process")
    else:
        print("⚠️ Auto Learning real audit test skipped or failed")
    
    print("\nRECOMMENDations:")
    print("1. Run a complete payroll audit with real PDF files to generate authentic data")
    print("2. Verify Auto Learning receives data during the AUTO_LEARNING phase")
    print("3. Check that pending items appear for manual review")
    print("4. Test approval workflow for pending items")
    
    return analysis_success

if __name__ == "__main__":
    main()
