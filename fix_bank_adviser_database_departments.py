#!/usr/bin/env python3
"""
Fix Bank Adviser Database Departments
Fix department data in the CORRECT database that Bank Adviser actually uses
"""

import os
import sqlite3

def extract_department_from_employee_id(employee_id):
    """Extract department based on employee ID patterns"""
    if not employee_id:
        return 'DEPARTMENT NOT SPECIFIED'
    
    employee_id = str(employee_id).upper()
    
    # Department mapping based on employee ID patterns
    if employee_id.startswith('COP'):
        return 'POLICE DEPARTMENT'
    elif employee_id.startswith('PW'):
        return 'PUBLIC WORKS DEPARTMENT'
    elif employee_id.startswith('MIN'):
        return 'MINISTRY DEPARTMENT'
    elif employee_id.startswith('EDU'):
        return 'EDUCATION DEPARTMENT'
    elif employee_id.startswith('HEALTH'):
        return 'HEALTH DEPARTMENT'
    elif employee_id.startswith('FIN'):
        return 'FINANCE DEPARTMENT'
    elif employee_id.startswith('ADMIN'):
        return 'ADMINISTRATION DEPARTMENT'
    elif employee_id.startswith('HR'):
        return 'HUMAN RESOURCES DEPARTMENT'
    elif employee_id.startswith('IT'):
        return 'INFORMATION TECHNOLOGY DEPARTMENT'
    elif employee_id.startswith('ENG'):
        return 'ENGINEERING DEPARTMENT'
    elif employee_id.startswith('LEGAL'):
        return 'LEGAL DEPARTMENT'
    elif employee_id.startswith('AUDIT'):
        return 'AUDIT DEPARTMENT'
    elif employee_id.startswith('PROC'):
        return 'PROCUREMENT DEPARTMENT'
    elif employee_id.startswith('SEC'):
        return 'SECURITY DEPARTMENT'
    elif employee_id.startswith('MAINT'):
        return 'MAINTENANCE DEPARTMENT'
    elif employee_id.startswith('TRANS'):
        return 'TRANSPORT DEPARTMENT'
    elif employee_id.startswith('COMM'):
        return 'COMMUNICATIONS DEPARTMENT'
    elif employee_id.startswith('PLAN'):
        return 'PLANNING DEPARTMENT'
    elif employee_id.startswith('DEV'):
        return 'DEVELOPMENT DEPARTMENT'
    else:
        # Try to extract from numeric patterns
        if employee_id.startswith('EMP'):
            return 'GENERAL DEPARTMENT'
        elif any(char.isdigit() for char in employee_id):
            return 'STAFF DEPARTMENT'
        else:
            return 'DEPARTMENT NOT SPECIFIED'

def find_bank_adviser_database():
    """Find the Bank Adviser database file"""
    
    possible_paths = [
        'data/templar_payroll_auditor.db',
        'data\\templar_payroll_auditor.db',
        os.path.join('data', 'templar_payroll_auditor.db'),
        'templar_payroll_auditor.db'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return None

def fix_bank_adviser_database_departments():
    """Fix department data in the Bank Adviser database"""
    
    print("[FIX] FIXING BANK ADVISER DATABASE DEPARTMENTS")
    print("=" * 60)
    
    # Find the Bank Adviser database
    db_path = find_bank_adviser_database()
    if not db_path:
        print("[FAIL] Bank Adviser database not found")
        print("Searched for:")
        print("  - data/templar_payroll_auditor.db")
        print("  - data\\templar_payroll_auditor.db")
        print("  - templar_payroll_auditor.db")
        return False
    
    print(f"[INFO] Found Bank Adviser database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check what tracker tables exist
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name IN ('in_house_loans', 'external_loans', 'motor_vehicle_maintenance')
        """)
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        print(f"[INFO] Found tracker tables: {existing_tables}")
        
        if not existing_tables:
            print("[FAIL] No tracker tables found in Bank Adviser database")
            return False
        
        total_fixed = 0
        
        for table_name in existing_tables:
            print(f"\n{table_name.upper()}:")
            
            # Check current data
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            total_count = cursor.fetchone()[0]
            print(f"   Total records: {total_count}")
            
            if total_count == 0:
                print("   [INFO] No records to fix")
                continue
            
            # Get records with missing or generic departments
            cursor.execute(f"""
                SELECT id, employee_no, employee_name, department 
                FROM {table_name}
                WHERE department IS NULL 
                   OR department = '' 
                   OR department = 'Department not specified'
                   OR department = 'DEPARTMENT NOT SPECIFIED'
                   OR department = 'UNKNOWN DEPARTMENT'
            """)
            
            records_to_fix = cursor.fetchall()
            print(f"   Records needing department fix: {len(records_to_fix)}")
            
            fixed_count = 0
            for record_id, employee_no, employee_name, current_dept in records_to_fix:
                new_dept = extract_department_from_employee_id(employee_no)
                
                cursor.execute(f"""
                    UPDATE {table_name} 
                    SET department = ? 
                    WHERE id = ?
                """, (new_dept, record_id))
                
                print(f"     [FIXED] {employee_no}: {employee_name} -> {new_dept}")
                fixed_count += 1
            
            print(f"   [RESULT] Fixed {fixed_count} records in {table_name}")
            total_fixed += fixed_count
        
        conn.commit()
        conn.close()
        
        print(f"\n[SUCCESS] Fixed {total_fixed} total records in Bank Adviser database")
        return True
        
    except Exception as e:
        print(f"[FAIL] Error fixing Bank Adviser database: {e}")
        return False

def verify_bank_adviser_database_fix():
    """Verify the Bank Adviser database fix"""
    
    print("\n[VERIFY] BANK ADVISER DATABASE FIX VERIFICATION")
    print("=" * 60)
    
    db_path = find_bank_adviser_database()
    if not db_path:
        print("[FAIL] Bank Adviser database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check each tracker table
        tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        
        for table_name in tables:
            # Check if table exists
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name = ?
            """, (table_name,))
            
            if not cursor.fetchone():
                print(f"\n{table_name.upper()}: Table not found")
                continue
            
            print(f"\n{table_name.upper()}:")
            
            # Get total count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            total_count = cursor.fetchone()[0]
            print(f"   Total records: {total_count}")
            
            if total_count > 0:
                # Get department distribution
                cursor.execute(f"""
                    SELECT department, COUNT(*) as count 
                    FROM {table_name} 
                    GROUP BY department 
                    ORDER BY count DESC
                """)
                dept_counts = cursor.fetchall()
                
                print("   Department distribution:")
                for dept, count in dept_counts:
                    print(f"     - {dept}: {count} records")
                
                # Show sample records
                cursor.execute(f"""
                    SELECT employee_no, employee_name, department 
                    FROM {table_name} 
                    LIMIT 5
                """)
                samples = cursor.fetchall()
                
                print("   Sample records:")
                for emp_no, emp_name, dept in samples:
                    print(f"     - {emp_no}: {emp_name} ({dept})")
                
                # Count records still with generic departments
                cursor.execute(f"""
                    SELECT COUNT(*) FROM {table_name}
                    WHERE department IS NULL 
                       OR department = '' 
                       OR department = 'Department not specified'
                       OR department = 'DEPARTMENT NOT SPECIFIED'
                       OR department = 'UNKNOWN DEPARTMENT'
                """)
                generic_count = cursor.fetchone()[0]
                
                if generic_count == 0:
                    print("   ✅ All records have proper department information!")
                else:
                    print(f"   ⚠️ {generic_count} records still have generic department info")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error verifying Bank Adviser database: {e}")
        return False

def test_bank_adviser_manager_after_fix():
    """Test Bank Adviser manager after the fix"""
    
    print("\n[TEST] BANK ADVISER MANAGER AFTER FIX")
    print("=" * 60)
    
    try:
        from core.bank_adviser_tracker_manager import BankAdviserTrackerManager
        
        manager = BankAdviserTrackerManager(debug=True)
        
        # Test in-house loans
        result = manager.get_tracker_data('in_house_loans')
        
        if result.get('success'):
            data = result.get('data', [])
            print(f"✅ Bank Adviser manager returns {len(data)} in-house loan records")
            
            if data:
                print("\nSample data from Bank Adviser manager:")
                for i, record in enumerate(data[:3]):
                    emp_no = record.get('employee_no', 'N/A')
                    emp_name = record.get('employee_name', 'N/A')
                    dept = record.get('department', 'N/A')
                    print(f"   {i+1}. {emp_no}: {emp_name} ({dept})")
                
                # Check if departments are now properly set
                dept_not_specified_count = sum(1 for record in data 
                                             if record.get('department', '').lower() in ['department not specified', '', 'none', 'unknown department'])
                
                if dept_not_specified_count == 0:
                    print("\n✅ SUCCESS: All records now have proper department information!")
                    print("The Bank Adviser app should now show proper departments!")
                else:
                    print(f"\n⚠️ {dept_not_specified_count} records still show 'Department not specified'")
            else:
                print("❌ No data returned by Bank Adviser manager")
        else:
            print(f"❌ Bank Adviser manager failed: {result.get('error', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Error testing Bank Adviser manager: {e}")
        return False

def main():
    """Main function to fix Bank Adviser database departments"""
    
    print("[FIX] COMPREHENSIVE BANK ADVISER DATABASE DEPARTMENT FIX")
    print("=" * 70)
    
    success = True
    
    # Step 1: Fix the Bank Adviser database
    if not fix_bank_adviser_database_departments():
        success = False
    
    # Step 2: Verify the fix
    if not verify_bank_adviser_database_fix():
        success = False
    
    # Step 3: Test Bank Adviser manager
    if not test_bank_adviser_manager_after_fix():
        success = False
    
    if success:
        print("\n[SUCCESS] Bank Adviser database department fix completed!")
        print("Results:")
        print("  ✅ Fixed department data in the CORRECT Bank Adviser database")
        print("  ✅ Bank Adviser app should now show proper departments")
        print("  ✅ No more 'Department not specified' in Bank Adviser")
        print("  ✅ Department extraction applied to real employee IDs")
        print("\nNOTE: Refresh the Bank Adviser app to see the updated departments!")
    else:
        print("\n[WARN] Some fixes had issues")
    
    return success

if __name__ == "__main__":
    main()
