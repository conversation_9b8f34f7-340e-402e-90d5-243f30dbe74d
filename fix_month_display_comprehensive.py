#!/usr/bin/env python3
"""
Fix Month Display Comprehensive
Replace all instances of 'Current' and 'Previous' with actual month names
"""

import os
import re

def fix_javascript_files():
    """Fix JavaScript files to use actual month names"""
    
    print("[FIX] FIXING JAVASCRIPT FILES")
    print("=" * 50)
    
    js_files = [
        'renderer.js',
        'ui/interactive_pre_reporting.js',
        'ui/phase_manager.js'
    ]
    
    fixes_applied = 0
    
    for js_file in js_files:
        if not os.path.exists(js_file):
            print(f"[SKIP] File not found: {js_file}")
            continue
        
        print(f"\nProcessing {js_file}:")
        
        try:
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Fix specific patterns
            replacements = [
                # Fix hardcoded "Current Period" and "Previous Period"
                (r"'Current Period'", "'${currentMonth} ${currentYear}'"),
                (r'"Current Period"', '"${currentMonth} ${currentYear}"'),
                (r"'Previous Period'", "'${previousMonth} ${previousYear}'"),
                (r'"Previous Period"', '"${previousMonth} ${previousYear}"'),
                
                # Fix template literals that use generic terms
                (r"Current Period →", "${previousMonth} ${previousYear} →"),
                (r"Previous Period", "${previousMonth} ${previousYear}"),
                (r"Current Period", "${currentMonth} ${currentYear}"),
                
                # Fix specific cases in renderer.js
                (r"const month1 = change\.previous_month \|\| 'Previous Period';", 
                 "const month1 = change.previous_month || `${change.previous_month || 'Previous'} ${change.previous_year || ''}`;"),
                (r"const month2 = change\.current_month \|\| 'Current Period';", 
                 "const month2 = change.current_month || `${change.current_month || 'Current'} ${change.current_year || ''}`;"),
                
                # Fix formatPeriodDisplay fallback
                (r"return 'Current Period → Previous Period';", 
                 "return `${this.sessionInfo?.currentMonth || 'Current'} ${this.sessionInfo?.currentYear || ''} → ${this.sessionInfo?.previousMonth || 'Previous'} ${this.sessionInfo?.previousYear || ''}`;"),
            ]
            
            file_fixes = 0
            for pattern, replacement in replacements:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    file_fixes += 1
                    print(f"   [FIXED] Pattern: {pattern[:50]}...")
            
            # Write back if changes were made
            if content != original_content:
                with open(js_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"   [OK] Applied {file_fixes} fixes to {js_file}")
                fixes_applied += file_fixes
            else:
                print(f"   [INFO] No changes needed in {js_file}")
                
        except Exception as e:
            print(f"   [FAIL] Error processing {js_file}: {e}")
    
    print(f"\n[RESULT] Applied {fixes_applied} total fixes to JavaScript files")
    return fixes_applied > 0

def fix_html_files():
    """Fix HTML files to use actual month names"""
    
    print("\n[FIX] FIXING HTML FILES")
    print("=" * 50)
    
    html_files = ['index.html']
    
    fixes_applied = 0
    
    for html_file in html_files:
        if not os.path.exists(html_file):
            print(f"[SKIP] File not found: {html_file}")
            continue
        
        print(f"\nProcessing {html_file}:")
        
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Fix HTML content
            replacements = [
                # Fix section headers
                (r'<h3><i class="fas fa-calendar-alt"></i> Current Month Payroll</h3>',
                 '<h3><i class="fas fa-calendar-alt"></i> <span id="current-month-label">Current Month</span> Payroll</h3>'),
                (r'<h3><i class="fas fa-calendar-check"></i> Previous Month Payroll</h3>',
                 '<h3><i class="fas fa-calendar-check"></i> <span id="previous-month-label">Previous Month</span> Payroll</h3>'),
                
                # Fix file upload descriptions
                (r'<p>Select your current month payroll PDF</p>',
                 '<p>Select your <span class="current-month-text">current month</span> payroll PDF</p>'),
                (r'<p>Select your previous month payroll PDF</p>',
                 '<p>Select your <span class="previous-month-text">previous month</span> payroll PDF</p>'),
            ]
            
            file_fixes = 0
            for pattern, replacement in replacements:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    file_fixes += 1
                    print(f"   [FIXED] Pattern: {pattern[:50]}...")
            
            # Write back if changes were made
            if content != original_content:
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"   [OK] Applied {file_fixes} fixes to {html_file}")
                fixes_applied += file_fixes
            else:
                print(f"   [INFO] No changes needed in {html_file}")
                
        except Exception as e:
            print(f"   [FAIL] Error processing {html_file}: {e}")
    
    print(f"\n[RESULT] Applied {fixes_applied} total fixes to HTML files")
    return fixes_applied > 0

def create_month_display_utility():
    """Create a utility script for consistent month display"""
    
    print("\n[FIX] CREATING MONTH DISPLAY UTILITY")
    print("=" * 50)
    
    utility_js = """
// Month Display Utility
// Provides consistent month name display throughout the application

class MonthDisplayUtility {
    constructor() {
        this.monthNames = {
            'JAN': 'January', 'JANUARY': 'January',
            'FEB': 'February', 'FEBRUARY': 'February', 
            'MAR': 'March', 'MARCH': 'March',
            'APR': 'April', 'APRIL': 'April',
            'MAY': 'May',
            'JUN': 'June', 'JUNE': 'June',
            'JUL': 'July', 'JULY': 'July',
            'AUG': 'August', 'AUGUST': 'August',
            'SEP': 'September', 'SEPTEMBER': 'September',
            'OCT': 'October', 'OCTOBER': 'October',
            'NOV': 'November', 'NOVEMBER': 'November',
            'DEC': 'December', 'DECEMBER': 'December'
        };
    }

    /**
     * Convert month abbreviation or name to full month name
     */
    getFullMonthName(month) {
        if (!month) return 'Unknown Month';
        
        const upperMonth = month.toString().toUpperCase();
        return this.monthNames[upperMonth] || month;
    }

    /**
     * Format period display with actual month names
     */
    formatPeriodDisplay(sessionInfo) {
        if (!sessionInfo) {
            return 'Period Comparison';
        }

        const previousMonth = this.getFullMonthName(sessionInfo.previousMonth || sessionInfo.previous_month);
        const currentMonth = this.getFullMonthName(sessionInfo.currentMonth || sessionInfo.current_month);
        const previousYear = sessionInfo.previousYear || sessionInfo.previous_year || '';
        const currentYear = sessionInfo.currentYear || sessionInfo.current_year || '';

        return `${previousMonth} ${previousYear} → ${currentMonth} ${currentYear}`;
    }

    /**
     * Update UI elements with actual month names
     */
    updateMonthLabelsInUI(sessionInfo) {
        if (!sessionInfo) return;

        const currentMonth = this.getFullMonthName(sessionInfo.currentMonth || sessionInfo.current_month);
        const previousMonth = this.getFullMonthName(sessionInfo.previousMonth || sessionInfo.previous_month);

        // Update current month labels
        const currentMonthLabels = document.querySelectorAll('#current-month-label, .current-month-text');
        currentMonthLabels.forEach(label => {
            label.textContent = currentMonth;
        });

        // Update previous month labels
        const previousMonthLabels = document.querySelectorAll('#previous-month-label, .previous-month-text');
        previousMonthLabels.forEach(label => {
            label.textContent = previousMonth;
        });

        // Update period comparison displays
        const periodDisplays = document.querySelectorAll('.compared-periods, .period-display');
        periodDisplays.forEach(display => {
            display.textContent = this.formatPeriodDisplay(sessionInfo);
        });

        console.log(`[MONTH-DISPLAY] Updated UI with: ${previousMonth} → ${currentMonth}`);
    }

    /**
     * Replace generic period text with actual month names in any string
     */
    replaceGenericPeriodText(text, sessionInfo) {
        if (!text || !sessionInfo) return text;

        const currentMonth = this.getFullMonthName(sessionInfo.currentMonth || sessionInfo.current_month);
        const previousMonth = this.getFullMonthName(sessionInfo.previousMonth || sessionInfo.previous_month);
        const currentYear = sessionInfo.currentYear || sessionInfo.current_year || '';
        const previousYear = sessionInfo.previousYear || sessionInfo.previous_year || '';

        return text
            .replace(/Current Period/g, `${currentMonth} ${currentYear}`.trim())
            .replace(/Previous Period/g, `${previousMonth} ${previousYear}`.trim())
            .replace(/Current Month/g, currentMonth)
            .replace(/Previous Month/g, previousMonth);
    }
}

// Create global instance
window.monthDisplayUtility = new MonthDisplayUtility();

// Auto-update month labels when session info is available
document.addEventListener('DOMContentLoaded', () => {
    // Listen for session info updates
    document.addEventListener('sessionInfoUpdated', (event) => {
        if (event.detail && event.detail.sessionInfo) {
            window.monthDisplayUtility.updateMonthLabelsInUI(event.detail.sessionInfo);
        }
    });
});

console.log('[MONTH-DISPLAY] Month Display Utility loaded');
"""
    
    try:
        with open('ui/month_display_utility.js', 'w', encoding='utf-8') as f:
            f.write(utility_js)
        
        print("[OK] Month display utility created: ui/month_display_utility.js")
        return True
        
    except Exception as e:
        print(f"[FAIL] Error creating month display utility: {e}")
        return False

def verify_month_display_fixes():
    """Verify that month display fixes are working"""
    
    print("\n[CHECK] VERIFYING MONTH DISPLAY FIXES")
    print("=" * 50)
    
    files_to_check = [
        'renderer.js',
        'ui/interactive_pre_reporting.js', 
        'ui/phase_manager.js',
        'index.html',
        'ui/month_display_utility.js'
    ]
    
    issues_found = 0
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"[MISSING] {file_path}")
            issues_found += 1
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for remaining hardcoded "Current Period" or "Previous Period"
            hardcoded_current = len(re.findall(r"['\"]Current Period['\"]", content))
            hardcoded_previous = len(re.findall(r"['\"]Previous Period['\"]", content))
            
            if hardcoded_current > 0 or hardcoded_previous > 0:
                print(f"[WARN] {file_path}: Found {hardcoded_current} 'Current Period' and {hardcoded_previous} 'Previous Period' instances")
                issues_found += hardcoded_current + hardcoded_previous
            else:
                print(f"[OK] {file_path}: No hardcoded period text found")
                
        except Exception as e:
            print(f"[FAIL] Error checking {file_path}: {e}")
            issues_found += 1
    
    if issues_found == 0:
        print("\n[SUCCESS] All month display fixes verified!")
    else:
        print(f"\n[WARN] Found {issues_found} remaining issues")
    
    return issues_found == 0

def main():
    """Main function to run all month display fixes"""
    
    print("[FIX] COMPREHENSIVE MONTH DISPLAY FIX")
    print("=" * 70)
    
    success = True
    
    # Step 1: Fix JavaScript files
    if not fix_javascript_files():
        print("[WARN] JavaScript fixes had issues")
    
    # Step 2: Fix HTML files  
    if not fix_html_files():
        print("[WARN] HTML fixes had issues")
    
    # Step 3: Create month display utility
    if not create_month_display_utility():
        success = False
    
    # Step 4: Verify fixes
    if not verify_month_display_fixes():
        print("[WARN] Some issues remain")
    
    if success:
        print("\n[SUCCESS] All month display fixes completed!")
        print("The system should now:")
        print("  - Show actual month names instead of 'Current' and 'Previous'")
        print("  - Use consistent month name formatting")
        print("  - Update UI labels dynamically based on session data")
        print("  - Have a utility for consistent month display")
    else:
        print("\n[WARN] Some month display fixes had issues")
    
    return success

if __name__ == "__main__":
    main()
