
// Report Generation Success Handler with Automatic Redirection
function handleReportGenerationSuccess(reportResult) {
    console.log('📊 Report generation completed successfully:', reportResult);
    
    // Show success notification
    showNotification('Report generated successfully! Redirecting to Report Manager...', 'success');
    
    // Wait 2 seconds then redirect to Report tab
    setTimeout(() => {
        console.log('🔄 Redirecting to Report Manager tab...');
        
        // Switch to Report Manager tab
        const reportTab = document.querySelector('[data-tab="report-manager"]');
        if (reportTab) {
            reportTab.click();
            
            // Refresh the reports list
            setTimeout(() => {
                if (typeof loadSavedReports === 'function') {
                    loadSavedReports();
                }
            }, 500);
        } else {
            console.warn('⚠️ Report Manager tab not found');
        }
    }, 2000);
}

// Enhanced Report Generation Function
async function generateReportWithRedirection(reportData, options = {}) {
    try {
        console.log('📊 Starting report generation with redirection...');
        
        // Call the existing report generation API
        const result = await window.api.generateReport(reportData, options);
        
        if (result.success) {
            // Handle successful generation
            handleReportGenerationSuccess(result);
            
            // Save to Report Manager
            try {
                const saveResult = await window.api.saveToReportManager({
                    report_id: reportData.id || `report_${Date.now()}`,
                    report_type: 'payroll_audit',
                    title: reportData.report_title || 'Payroll Audit Report',
                    description: `Generated on ${new Date().toLocaleDateString()}`,
                    file_paths: result.file_paths || {},
                    metadata: {
                        session_id: reportData.session_id,
                        generated_at: new Date().toISOString(),
                        total_changes: reportData.selected_changes?.length || 0
                    }
                });
                
                if (saveResult.success) {
                    console.log('✅ Report saved to Report Manager successfully');
                } else {
                    console.warn('⚠️ Failed to save to Report Manager:', saveResult.error);
                }
            } catch (saveError) {
                console.error('❌ Error saving to Report Manager:', saveError);
            }
        } else {
            throw new Error(result.error || 'Report generation failed');
        }
        
        return result;
        
    } catch (error) {
        console.error('❌ Report generation with redirection failed:', error);
        showNotification('Report generation failed: ' + error.message, 'error');
        throw error;
    }
}

// Override the existing generateReportsWithProgress function
if (typeof window.generateReportsWithProgress === 'function') {
    window.originalGenerateReportsWithProgress = window.generateReportsWithProgress;
}

window.generateReportsWithProgress = generateReportWithRedirection;

console.log('✅ Report redirection functionality loaded');
