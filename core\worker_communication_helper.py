#!/usr/bin/env python3
"""
Worker Communication Helper
Provides message-based communication for worker threads
PERMANENT SOLUTION: Eliminates function cloning issues by using serializable messages
"""

import sys
import json
import time
from datetime import datetime

class WorkerCommunicator:
    """
    Handles message-based communication between Python workers and main thread
    Uses stdout with structured message format for reliable communication
    """
    
    def __init__(self, worker_id=None, enable_progress=True, enable_realtime=True):
        self.worker_id = worker_id or f"worker_{int(time.time())}"
        self.enable_progress = enable_progress
        self.enable_realtime = enable_realtime
        self.start_time = time.time()
    
    def send_progress_update(self, percentage, message, phase=None, details=None):
        """
        Send progress update to main thread
        PERMANENT FIX: Uses structured message format instead of callback functions
        """
        if not self.enable_progress:
            return
        
        try:
            progress_data = {
                "type": "progress",
                "worker_id": self.worker_id,
                "percentage": percentage,
                "message": message,
                "phase": phase,
                "details": details,
                "timestamp": datetime.now().isoformat(),
                "elapsed_time": time.time() - self.start_time
            }
            
            # Send structured message via stdout
            print(f"PROGRESS_UPDATE:{json.dumps(progress_data)}")
            sys.stdout.flush()
            
        except Exception as e:
            # Fallback to simple message if JSON fails
            print(f"PROGRESS: {percentage}% - {message}")
            sys.stdout.flush()
    
    def send_realtime_update(self, update_type, data, message=None):
        """
        Send real-time update to main thread
        PERMANENT FIX: Uses structured message format instead of callback functions
        """
        if not self.enable_realtime:
            return
        
        try:
            realtime_data = {
                "type": update_type,
                "worker_id": self.worker_id,
                "data": data,
                "message": message,
                "timestamp": datetime.now().isoformat()
            }
            
            # Send structured message via stdout
            print(f"REALTIME_UPDATE:{json.dumps(realtime_data)}")
            sys.stdout.flush()
            
        except Exception as e:
            # Fallback to simple message if JSON fails
            print(f"REALTIME: {update_type} - {message or str(data)}")
            sys.stdout.flush()
    
    def send_extraction_progress(self, current_page, total_pages, employee_count=None):
        """Send extraction-specific progress update"""
        percentage = (current_page / total_pages) * 100 if total_pages > 0 else 0
        message = f"Extracting page {current_page} of {total_pages}"
        
        if employee_count:
            message += f" ({employee_count} employees processed)"
        
        self.send_progress_update(percentage, message, "EXTRACTION", {
            "current_page": current_page,
            "total_pages": total_pages,
            "employee_count": employee_count
        })
    
    def send_comparison_progress(self, current_employee, total_employees, changes_found=None):
        """Send comparison-specific progress update"""
        percentage = (current_employee / total_employees) * 100 if total_employees > 0 else 0
        message = f"Comparing employee {current_employee} of {total_employees}"
        
        if changes_found is not None:
            message += f" ({changes_found} changes found)"
        
        self.send_progress_update(percentage, message, "COMPARISON", {
            "current_employee": current_employee,
            "total_employees": total_employees,
            "changes_found": changes_found
        })
    
    def send_tracker_progress(self, items_processed, total_items, tracker_type=None):
        """Send tracker feeding progress update"""
        percentage = (items_processed / total_items) * 100 if total_items > 0 else 0
        message = f"Processing tracker item {items_processed} of {total_items}"
        
        if tracker_type:
            message += f" ({tracker_type})"
        
        self.send_progress_update(percentage, message, "TRACKER_FEEDING", {
            "items_processed": items_processed,
            "total_items": total_items,
            "tracker_type": tracker_type
        })
    
    def send_phase_completion(self, phase, success=True, details=None):
        """Send phase completion notification"""
        self.send_realtime_update("phase_complete", {
            "phase": phase,
            "success": success,
            "details": details
        }, f"Phase {phase} {'completed' if success else 'failed'}")
    
    def send_error(self, error_message, error_type="general", details=None):
        """Send error notification"""
        self.send_realtime_update("error", {
            "error_type": error_type,
            "error_message": error_message,
            "details": details
        }, f"Error: {error_message}")
    
    def send_warning(self, warning_message, warning_type="general", details=None):
        """Send warning notification"""
        self.send_realtime_update("warning", {
            "warning_type": warning_type,
            "warning_message": warning_message,
            "details": details
        }, f"Warning: {warning_message}")

# Global communicator instance for easy access
_global_communicator = None

def get_worker_communicator(worker_id=None, enable_progress=True, enable_realtime=True):
    """
    Get or create global worker communicator instance
    PERMANENT SOLUTION: Provides consistent communication interface
    """
    global _global_communicator
    
    if _global_communicator is None:
        _global_communicator = WorkerCommunicator(worker_id, enable_progress, enable_realtime)
    
    return _global_communicator

def send_progress(percentage, message, phase=None, details=None):
    """Convenience function for sending progress updates"""
    communicator = get_worker_communicator()
    communicator.send_progress_update(percentage, message, phase, details)

def send_realtime(update_type, data, message=None):
    """Convenience function for sending real-time updates"""
    communicator = get_worker_communicator()
    communicator.send_realtime_update(update_type, data, message)

def send_extraction_progress(current_page, total_pages, employee_count=None):
    """Convenience function for extraction progress"""
    communicator = get_worker_communicator()
    communicator.send_extraction_progress(current_page, total_pages, employee_count)

def send_comparison_progress(current_employee, total_employees, changes_found=None):
    """Convenience function for comparison progress"""
    communicator = get_worker_communicator()
    communicator.send_comparison_progress(current_employee, total_employees, changes_found)

def send_tracker_progress(items_processed, total_items, tracker_type=None):
    """Convenience function for tracker progress"""
    communicator = get_worker_communicator()
    communicator.send_tracker_progress(items_processed, total_items, tracker_type)

# Example usage for testing
if __name__ == "__main__":
    print("Testing Worker Communication Helper")
    
    # Initialize communicator
    comm = WorkerCommunicator("test_worker", True, True)
    
    # Test progress updates
    for i in range(0, 101, 20):
        comm.send_progress_update(i, f"Processing step {i//20 + 1}", "TEST_PHASE")
        time.sleep(0.1)
    
    # Test real-time updates
    comm.send_realtime_update("test_data", {"test": "value"}, "Test message")
    comm.send_phase_completion("TEST_PHASE", True, {"items_processed": 5})
    
    print("Worker communication test completed")
