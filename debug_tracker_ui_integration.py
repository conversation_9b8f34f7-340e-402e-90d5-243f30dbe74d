#!/usr/bin/env python3
"""
Debug Tracker UI Integration
Check why tracker data isn't showing in the app interface
"""

import os
import sqlite3
import json

def check_bank_adviser_tracker_manager():
    """Check if Bank Adviser tracker manager can access the data"""
    
    print("[DEBUG] BANK ADVISER TRACKER MANAGER ACCESS")
    print("=" * 60)
    
    try:
        # Try to import and use the Bank Adviser tracker manager
        from core.bank_adviser_tracker_manager import BankAdviserTrackerManager
        
        manager = BankAdviserTrackerManager(debug=True)
        
        # Test each tracker category
        categories = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        
        for category in categories:
            print(f"\n{category.upper()}:")
            result = manager.get_tracker_data(category)
            
            if result.get('success'):
                data = result.get('data', [])
                print(f"   Records found: {len(data)}")
                
                if data:
                    print("   Sample records:")
                    for i, record in enumerate(data[:3]):
                        emp_no = record.get('employee_no', 'N/A')
                        emp_name = record.get('employee_name', 'N/A')
                        dept = record.get('department', 'N/A')
                        print(f"     {i+1}. {emp_no}: {emp_name} ({dept})")
                else:
                    print("   [INFO] No records returned by manager")
            else:
                print(f"   [ERROR] Manager failed: {result.get('error', 'Unknown error')}")
        
        return True
        
    except ImportError as e:
        print(f"[FAIL] Cannot import Bank Adviser tracker manager: {e}")
        return False
    except Exception as e:
        print(f"[FAIL] Error testing tracker manager: {e}")
        return False

def check_database_direct_access():
    """Check direct database access to tracker tables"""
    
    print("\n[DEBUG] DIRECT DATABASE ACCESS")
    print("=" * 60)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check each table structure and data
        tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        
        for table in tables:
            print(f"\n{table.upper()}:")
            
            # Check table structure
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            print(f"   Columns: {[col[1] for col in columns]}")
            
            # Check data count
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   Total records: {count}")
            
            if count > 0:
                # Check sample data
                cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                records = cursor.fetchall()
                column_names = [col[1] for col in columns]
                
                print("   Sample records:")
                for i, record in enumerate(records):
                    record_dict = dict(zip(column_names, record))
                    emp_no = record_dict.get('employee_no', 'N/A')
                    emp_name = record_dict.get('employee_name', 'N/A')
                    dept = record_dict.get('department', 'N/A')
                    print(f"     {i+1}. {emp_no}: {emp_name} ({dept})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error with direct database access: {e}")
        return False

def test_bank_adviser_api_simulation():
    """Simulate the Bank Adviser API call that the UI makes"""
    
    print("\n[DEBUG] BANK ADVISER API SIMULATION")
    print("=" * 60)
    
    try:
        # Simulate the API call that bank_adviser.js makes
        # This is what happens when the UI calls: window.electronAPI.invoke('bank-adviser-get-tracker-data', {...})
        
        from core.bank_adviser_tracker_manager import BankAdviserTrackerManager
        
        manager = BankAdviserTrackerManager(debug=True)
        
        # Simulate the exact call the UI makes
        query_data = {
            'category': 'in_house_loans',
            'searchTerm': '',
            'periodFilter': ''
        }
        
        print(f"Simulating API call with: {query_data}")
        
        result = manager.get_tracker_data(
            query_data['category'],
            query_data.get('searchTerm', ''),
            query_data.get('periodFilter', '')
        )
        
        print(f"API Result: {json.dumps(result, indent=2)}")
        
        if result.get('success') and result.get('data'):
            print(f"\n✅ API would return {len(result['data'])} records to the UI")
            
            # Show what the UI would receive
            for i, record in enumerate(result['data'][:3]):
                print(f"   Record {i+1}: {record}")
        else:
            print("❌ API would return no data to the UI")
            print(f"   Reason: {result.get('error', 'No data found')}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Error simulating API call: {e}")
        return False

def check_session_filtering():
    """Check if session filtering is preventing data from showing"""
    
    print("\n[DEBUG] SESSION FILTERING CHECK")
    print("=" * 60)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current sessions
        cursor.execute("SELECT session_id, created_at FROM sessions ORDER BY created_at DESC LIMIT 5")
        sessions = cursor.fetchall()
        
        print("Recent sessions:")
        for session_id, created_at in sessions:
            print(f"   - {session_id} ({created_at})")
        
        # Check if tracker data has session filtering
        cursor.execute("SELECT DISTINCT source_session FROM in_house_loans WHERE source_session IS NOT NULL")
        loan_sessions = cursor.fetchall()
        
        print(f"\nTracker data sessions:")
        for session, in loan_sessions:
            cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?", (session,))
            count = cursor.fetchone()[0]
            print(f"   - {session}: {count} loans")
        
        # Check if Bank Adviser is filtering by session
        print("\nChecking if Bank Adviser filters by session...")
        
        # Test without session filter
        cursor.execute("SELECT COUNT(*) FROM in_house_loans")
        total_loans = cursor.fetchone()[0]
        print(f"   Total loans (no filter): {total_loans}")
        
        # Test with session filter (if Bank Adviser uses it)
        if sessions:
            latest_session = sessions[0][0]
            cursor.execute("SELECT COUNT(*) FROM in_house_loans WHERE source_session = ?", (latest_session,))
            session_loans = cursor.fetchone()[0]
            print(f"   Loans for latest session ({latest_session}): {session_loans}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error checking session filtering: {e}")
        return False

def main():
    """Main debug function"""
    
    print("[DEBUG] COMPREHENSIVE TRACKER UI INTEGRATION DEBUG")
    print("=" * 70)
    
    success = True
    
    # Step 1: Check direct database access
    if not check_database_direct_access():
        success = False
    
    # Step 2: Check Bank Adviser tracker manager
    if not check_bank_adviser_tracker_manager():
        success = False
    
    # Step 3: Simulate API call
    if not test_bank_adviser_api_simulation():
        success = False
    
    # Step 4: Check session filtering
    if not check_session_filtering():
        success = False
    
    print("\n[SUMMARY] TRACKER UI INTEGRATION DEBUG RESULTS")
    print("=" * 70)
    
    if success:
        print("✅ Debug completed - check results above for issues")
    else:
        print("❌ Debug found critical issues")
    
    print("\nPOSSIBLE REASONS DATA ISN'T SHOWING:")
    print("1. Bank Adviser UI is filtering by session ID")
    print("2. Bank Adviser tracker manager has different database connection")
    print("3. UI is looking for data in different table structure")
    print("4. API calls are failing silently")
    print("5. Frontend JavaScript errors preventing data display")
    
    return success

if __name__ == "__main__":
    main()
