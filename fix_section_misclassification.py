#!/usr/bin/env python3
"""
Fix Section Misclassification Issues
Corrects items that are classified under wrong sections, particularly Educational Subsidy
appearing under Personal Details instead of Earnings.
"""

import os
import sys
import sqlite3
from datetime import datetime

def fix_section_misclassification():
    """Fix section misclassification issues in the database"""
    
    print("🔧 FIXING SECTION MISCLASSIFICATION ISSUES")
    print("=" * 60)
    
    # Database path
    db_path = os.path.join(os.path.dirname(__file__), 'payroll_auditor.db')
    
    if not os.path.exists(db_path):
        print("❌ Database not found. Please run a payroll audit first.")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Define misclassification fixes
        section_fixes = {
            # Items that should be in EARNINGS but might be elsewhere
            'EARNINGS': [
                'EDUCATIONAL SUBSIDY',
                'EDUCATION SUBSIDY', 
                'TRANSPORT ALLOWANCE',
                'OVERTIM<PERSON> ALLOWANCE',
                'RESPONSIBILITY ALLOWANCE',
                'LEAVE ALLOWANCE',
                'RENT ELEMENT',
                'FUEL ELEMENT',
                '2ND FUEL ELEMENT',
                'SUBSISTENCE ALLOWANCE'
            ],
            # Items that should be in DEDUCTIONS
            'DEDUCTIONS': [
                'INCOME TAX',
                'SSF EMPLOYEE',
                'WELFARE FUND',
                'TITHES',
                'CREDIT UNION',
                'PROVIDENT FUND'
            ],
            # Items that should be in PERSONAL DETAILS
            'PERSONAL DETAILS': [
                'EMPLOYEE NO.',
                'EMPLOYEE NAME',
                'SSF NO.',
                'GHANA CARD ID',
                'SECTION',
                'DEPARTMENT',
                'JOB TITLE'
            ]
        }
        
        total_fixes = 0
        
        print("\n1. 🔍 CHECKING FOR MISCLASSIFIED ITEMS:")
        
        # Check comparison_results table
        cursor.execute("SELECT COUNT(*) FROM comparison_results")
        comparison_count = cursor.fetchone()[0]
        print(f"   📊 Found {comparison_count} comparison results to check")
        
        # Fix misclassifications in comparison_results
        for correct_section, items in section_fixes.items():
            for item_name in items:
                # Find misclassified items
                cursor.execute("""
                    SELECT id, section, item_name, employee_id 
                    FROM comparison_results 
                    WHERE UPPER(item_name) = UPPER(?) AND UPPER(section) != UPPER(?)
                """, (item_name, correct_section))
                
                misclassified = cursor.fetchall()
                
                if misclassified:
                    print(f"\n   🔧 Fixing {len(misclassified)} '{item_name}' items:")
                    print(f"      Moving from various sections to '{correct_section}'")
                    
                    for record in misclassified:
                        record_id, old_section, item, employee_id = record
                        print(f"      • Employee {employee_id}: {old_section} → {correct_section}")
                        
                        # Update the section
                        cursor.execute("""
                            UPDATE comparison_results 
                            SET section = ?, updated_at = CURRENT_TIMESTAMP
                            WHERE id = ?
                        """, (correct_section, record_id))
                        
                        total_fixes += 1
        
        print(f"\n2. 🔧 FIXING EMPLOYEE DATA SECTIONS:")
        
        # Check employees table for section misclassifications
        cursor.execute("SELECT COUNT(*) FROM employees")
        employee_count = cursor.fetchone()[0]
        print(f"   📊 Found {employee_count} employee records to check")
        
        # Fix any employee data that might have section info in wrong fields
        # This is more about data integrity than section classification
        
        print(f"\n3. 📝 UPDATING DICTIONARY CLASSIFICATIONS:")
        
        # Update dictionary if it exists
        try:
            # Check if dictionary table exists
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='dictionary_items'
            """)
            
            if cursor.fetchone():
                # Update dictionary items with correct sections
                for correct_section, items in section_fixes.items():
                    for item_name in items:
                        cursor.execute("""
                            UPDATE dictionary_items 
                            SET section_name = ?, updated_at = CURRENT_TIMESTAMP
                            WHERE UPPER(item_name) = UPPER(?) AND UPPER(section_name) != UPPER(?)
                        """, (correct_section, item_name, correct_section))
                        
                        if cursor.rowcount > 0:
                            print(f"   ✅ Updated dictionary: {item_name} → {correct_section}")
                            total_fixes += cursor.rowcount
            else:
                print("   ℹ️ No dictionary table found - skipping dictionary updates")
                
        except Exception as e:
            print(f"   ⚠️ Dictionary update error: {e}")
        
        # Commit all changes
        conn.commit()
        
        print(f"\n✅ SECTION MISCLASSIFICATION FIX COMPLETE")
        print(f"   📊 Total fixes applied: {total_fixes}")
        print(f"   🕒 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Verify fixes
        print(f"\n4. 🔍 VERIFICATION:")
        
        # Check Educational Subsidy specifically
        cursor.execute("""
            SELECT section, COUNT(*) as count
            FROM comparison_results 
            WHERE UPPER(item_name) LIKE '%EDUCATIONAL%SUBSIDY%'
            GROUP BY section
        """)
        
        educational_subsidy_sections = cursor.fetchall()
        if educational_subsidy_sections:
            print("   📋 Educational Subsidy distribution:")
            for section, count in educational_subsidy_sections:
                status = "✅" if section.upper() == "EARNINGS" else "⚠️"
                print(f"      {status} {section}: {count} items")
        else:
            print("   ℹ️ No Educational Subsidy items found in database")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing section misclassifications: {e}")
        return False

if __name__ == "__main__":
    success = fix_section_misclassification()
    sys.exit(0 if success else 1)
