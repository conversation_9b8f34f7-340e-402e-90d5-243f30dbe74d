#!/usr/bin/env python3
"""
Fix Ghana Card ID Misclassification Issues
Resolves cases where Ghana Card ID appears in place of Employee Name,
especially for employees with PW#### employee numbers.
"""

import os
import sys
import sqlite3
from datetime import datetime

def fix_ghana_card_misclassification():
    """Fix Ghana Card ID misclassification issues in the database"""
    
    print("[FIX] FIXING GHANA CARD ID MISCLASSIFICATION ISSUES")
    print("=" * 60)
    
    # Database path - try multiple possible names
    possible_db_names = ['payroll_auditor.db', 'payroll_audit.db', 'templar_payroll_auditor.db']
    db_path = None

    for db_name in possible_db_names:
        test_path = os.path.join(os.path.dirname(__file__), db_name)
        if os.path.exists(test_path):
            db_path = test_path
            print(f"   [INFO] Found database: {db_name}")
            break
    
    if not db_path:
        print("   [FAIL] Database not found. Please run a payroll audit first.")
        print("   [INFO] Looked for:", ', '.join(possible_db_names))
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        total_fixes = 0
        
        print("\n1. [CHECK] CHECKING FOR GHANA CARD ID MISCLASSIFICATIONS:")

        # Check comparison_results for Ghana Card ID in employee_name field
        cursor.execute("""
            SELECT DISTINCT employee_id, employee_name
            FROM comparison_results
            WHERE employee_name LIKE '%Ghana Card%'
               OR employee_name LIKE '%GHANA CARD%'
               OR employee_name LIKE 'GHA-%'
               OR employee_name = 'Ghana Card ID'
        """)
        
        misclassified_employees = cursor.fetchall()
        print(f"   [DATA] Found {len(misclassified_employees)} employees with Ghana Card ID as name")

        for employee in misclassified_employees:
            emp_id, emp_name = employee

            print(f"   [FIX] Fixing employee {emp_id}: '{emp_name}' → 'EMPLOYEE_{emp_id}'")

            # Update employee name in comparison_results
            cursor.execute("""
                UPDATE comparison_results
                SET employee_name = ?
                WHERE employee_id = ?
            """, (f"EMPLOYEE_{emp_id}", emp_id))

            total_fixes += cursor.rowcount
        
        print(f"\n2. [CHECK] CHECKING FOR PW EMPLOYEE PATTERNS:")

        # Check for PW#### employees with potential name issues
        cursor.execute("""
            SELECT DISTINCT employee_id, employee_name
            FROM comparison_results
            WHERE employee_id LIKE 'PW%'
              AND (employee_name LIKE '%Ghana Card%'
                   OR employee_name LIKE 'GHA-%'
                   OR employee_name = employee_id
                   OR LENGTH(employee_name) < 5)
        """)

        pw_employees = cursor.fetchall()
        print(f"   [DATA] Found {len(pw_employees)} PW#### employees with name issues")

        for employee in pw_employees:
            emp_id, emp_name = employee
            new_name = f"PUBLIC_WORKS_EMPLOYEE_{emp_id}"

            print(f"   [FIX] Fixing PW employee {emp_id}: '{emp_name}' → '{new_name}'")

            # Update comparison results
            cursor.execute("""
                UPDATE comparison_results
                SET employee_name = ?
                WHERE employee_id = ?
            """, (new_name, emp_id))

            total_fixes += cursor.rowcount
        
        print(f"\n3. [CHECK] CHECKING FOR SECTION NAME AS EMPLOYEE NAME:")

        # Check for cases where section names appear as employee names
        section_names = ['PERSONAL DETAILS', 'EARNINGS', 'DEDUCTIONS', 'LOANS', 'EMPLOYERS CONTRIBUTION', 'EMPLOYEE BANK DETAILS']

        for section_name in section_names:
            cursor.execute("""
                SELECT DISTINCT employee_id, employee_name
                FROM comparison_results
                WHERE employee_name = ?
            """, (section_name,))

            section_misclassified = cursor.fetchall()

            if section_misclassified:
                print(f"   [DATA] Found {len(section_misclassified)} employees with '{section_name}' as name")

                for employee in section_misclassified:
                    emp_id, emp_name = employee
                    new_name = f"EMPLOYEE_{emp_id}"

                    print(f"   [FIX] Fixing section misclassification {emp_id}: '{emp_name}' → '{new_name}'")

                    # Update comparison results
                    cursor.execute("""
                        UPDATE comparison_results
                        SET employee_name = ?
                        WHERE employee_id = ?
                    """, (new_name, emp_id))

                    total_fixes += cursor.rowcount
        
        # Commit all changes
        conn.commit()
        
        print(f"\n[OK] GHANA CARD ID MISCLASSIFICATION FIX COMPLETE")
        print(f"   [DATA] Total fixes applied: {total_fixes}")
        print(f"   [TIME] Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Verification
        print(f"\n4. [CHECK] VERIFICATION:")

        # Check remaining Ghana Card ID issues
        cursor.execute("""
            SELECT COUNT(DISTINCT employee_name) as count
            FROM comparison_results
            WHERE employee_name LIKE '%Ghana Card%'
               OR employee_name LIKE 'GHA-%'
               OR employee_name = 'Ghana Card ID'
        """)

        remaining_issues = cursor.fetchone()[0]
        if remaining_issues == 0:
            print("   [OK] No remaining Ghana Card ID misclassifications found")
        else:
            print(f"   [WARN] {remaining_issues} Ghana Card ID issues still remain")

        # Check PW employees specifically
        cursor.execute("""
            SELECT DISTINCT employee_id, employee_name
            FROM comparison_results
            WHERE employee_id LIKE 'PW%'
            ORDER BY employee_id
            LIMIT 5
        """)

        pw_sample = cursor.fetchall()
        if pw_sample:
            print("   [LIST] Sample PW employee names after fix:")
            for emp_id, emp_name in pw_sample:
                status = "[OK]" if not any(x in emp_name.lower() for x in ['ghana', 'card', 'gha-']) else "[WARN]"
                print(f"      {status} {emp_id}: {emp_name}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error fixing Ghana Card ID misclassifications: {e}")
        return False

if __name__ == "__main__":
    success = fix_ghana_card_misclassification()
    sys.exit(0 if success else 1)
