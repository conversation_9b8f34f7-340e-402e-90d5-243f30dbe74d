#!/usr/bin/env python3
"""
Fix Ghana Card ID Misclassification Issues
Resolves cases where Ghana Card ID appears in place of Employee Name,
especially for employees with PW#### employee numbers.
"""

import os
import sys
import sqlite3
from datetime import datetime

def fix_ghana_card_misclassification():
    """Fix Ghana Card ID misclassification issues in the database"""
    
    print("🔧 FIXING GHANA CARD ID MISCLASSIFICATION ISSUES")
    print("=" * 60)
    
    # Database path
    db_path = os.path.join(os.path.dirname(__file__), 'payroll_auditor.db')
    
    if not os.path.exists(db_path):
        print("❌ Database not found. Please run a payroll audit first.")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        total_fixes = 0
        
        print("\n1. 🔍 CHECKING FOR GHANA CARD ID MISCLASSIFICATIONS:")
        
        # Check employees table for Ghana Card ID in employee_name field
        cursor.execute("""
            SELECT id, employee_no, employee_name, department
            FROM employees 
            WHERE employee_name LIKE '%Ghana Card%' 
               OR employee_name LIKE '%GHANA CARD%'
               OR employee_name LIKE 'GHA-%'
               OR employee_name = 'Ghana Card ID'
        """)
        
        misclassified_employees = cursor.fetchall()
        print(f"   📊 Found {len(misclassified_employees)} employees with Ghana Card ID as name")
        
        for employee in misclassified_employees:
            emp_id, emp_no, emp_name, department = employee
            
            print(f"   🔧 Fixing employee {emp_no}: '{emp_name}' → 'EMPLOYEE_{emp_no}'")
            
            # Update employee name
            cursor.execute("""
                UPDATE employees 
                SET employee_name = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (f"EMPLOYEE_{emp_no}", emp_id))
            
            total_fixes += 1
        
        print(f"\n2. 🔍 CHECKING COMPARISON RESULTS:")
        
        # Check comparison_results table for Ghana Card ID in employee_name
        cursor.execute("""
            SELECT DISTINCT employee_id, employee_name
            FROM comparison_results 
            WHERE employee_name LIKE '%Ghana Card%' 
               OR employee_name LIKE '%GHANA CARD%'
               OR employee_name LIKE 'GHA-%'
               OR employee_name = 'Ghana Card ID'
        """)
        
        misclassified_comparisons = cursor.fetchall()
        print(f"   📊 Found {len(misclassified_comparisons)} comparison records with Ghana Card ID as name")
        
        for comparison in misclassified_comparisons:
            emp_id, emp_name = comparison
            
            # Get the correct employee number
            cursor.execute("SELECT employee_no FROM employees WHERE id = ?", (emp_id,))
            emp_no_result = cursor.fetchone()
            
            if emp_no_result:
                emp_no = emp_no_result[0]
                new_name = f"EMPLOYEE_{emp_no}"
                
                print(f"   🔧 Fixing comparison records for employee {emp_no}: '{emp_name}' → '{new_name}'")
                
                # Update all comparison results for this employee
                cursor.execute("""
                    UPDATE comparison_results 
                    SET employee_name = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE employee_id = ?
                """, (new_name, emp_id))
                
                total_fixes += cursor.rowcount
        
        print(f"\n3. 🔍 CHECKING FOR EMPLOYEE NUMBER PATTERNS:")
        
        # Special focus on PW#### employees as mentioned in the issue
        cursor.execute("""
            SELECT id, employee_no, employee_name
            FROM employees 
            WHERE employee_no LIKE 'PW%' 
              AND (employee_name LIKE '%Ghana Card%' 
                   OR employee_name LIKE 'GHA-%'
                   OR employee_name = employee_no
                   OR LENGTH(employee_name) < 5)
        """)
        
        pw_employees = cursor.fetchall()
        print(f"   📊 Found {len(pw_employees)} PW#### employees with name issues")
        
        for employee in pw_employees:
            emp_id, emp_no, emp_name = employee
            new_name = f"PUBLIC_WORKS_EMPLOYEE_{emp_no}"
            
            print(f"   🔧 Fixing PW employee {emp_no}: '{emp_name}' → '{new_name}'")
            
            # Update employee name
            cursor.execute("""
                UPDATE employees 
                SET employee_name = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (new_name, emp_id))
            
            # Update comparison results
            cursor.execute("""
                UPDATE comparison_results 
                SET employee_name = ?, updated_at = CURRENT_TIMESTAMP
                WHERE employee_id = ?
            """, (new_name, emp_id))
            
            total_fixes += 1 + cursor.rowcount
        
        print(f"\n4. 🔍 CHECKING FOR SECTION NAME AS EMPLOYEE NAME:")
        
        # Check for cases where section names appear as employee names
        section_names = ['PERSONAL DETAILS', 'EARNINGS', 'DEDUCTIONS', 'LOANS', 'EMPLOYERS CONTRIBUTION', 'EMPLOYEE BANK DETAILS']
        
        for section_name in section_names:
            cursor.execute("""
                SELECT id, employee_no, employee_name
                FROM employees 
                WHERE employee_name = ?
            """, (section_name,))
            
            section_misclassified = cursor.fetchall()
            
            if section_misclassified:
                print(f"   📊 Found {len(section_misclassified)} employees with '{section_name}' as name")
                
                for employee in section_misclassified:
                    emp_id, emp_no, emp_name = employee
                    new_name = f"EMPLOYEE_{emp_no}"
                    
                    print(f"   🔧 Fixing section misclassification {emp_no}: '{emp_name}' → '{new_name}'")
                    
                    # Update employee name
                    cursor.execute("""
                        UPDATE employees 
                        SET employee_name = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """, (new_name, emp_id))
                    
                    # Update comparison results
                    cursor.execute("""
                        UPDATE comparison_results 
                        SET employee_name = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE employee_id = ?
                    """, (new_name, emp_id))
                    
                    total_fixes += 1 + cursor.rowcount
        
        # Commit all changes
        conn.commit()
        
        print(f"\n✅ GHANA CARD ID MISCLASSIFICATION FIX COMPLETE")
        print(f"   📊 Total fixes applied: {total_fixes}")
        print(f"   🕒 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Verification
        print(f"\n5. 🔍 VERIFICATION:")
        
        # Check remaining Ghana Card ID issues
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM employees 
            WHERE employee_name LIKE '%Ghana Card%' 
               OR employee_name LIKE 'GHA-%'
               OR employee_name = 'Ghana Card ID'
        """)
        
        remaining_issues = cursor.fetchone()[0]
        if remaining_issues == 0:
            print("   ✅ No remaining Ghana Card ID misclassifications found")
        else:
            print(f"   ⚠️ {remaining_issues} Ghana Card ID issues still remain")
        
        # Check PW employees specifically
        cursor.execute("""
            SELECT employee_no, employee_name
            FROM employees 
            WHERE employee_no LIKE 'PW%'
            ORDER BY employee_no
            LIMIT 5
        """)
        
        pw_sample = cursor.fetchall()
        if pw_sample:
            print("   📋 Sample PW employee names after fix:")
            for emp_no, emp_name in pw_sample:
                status = "✅" if not any(x in emp_name.lower() for x in ['ghana', 'card', 'gha-']) else "⚠️"
                print(f"      {status} {emp_no}: {emp_name}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing Ghana Card ID misclassifications: {e}")
        return False

if __name__ == "__main__":
    success = fix_ghana_card_misclassification()
    sys.exit(0 if success else 1)
