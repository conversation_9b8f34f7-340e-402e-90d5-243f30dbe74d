
// Universal Formatting Utility
// Provides consistent number formatting throughout the application

class UniversalFormattingUtility {
    constructor() {
        // Configuration for number formatting
        this.config = {
            numericDifferencePrecision: 2,
            percentagePrecision: 0,
            currencyPrecision: 2,
            defaultPrecision: 2
        };
    }

    /**
     * Format numeric difference to 2 decimal places
     */
    formatNumericDifference(value) {
        if (value === null || value === undefined || value === '' || isNaN(value)) {
            return 'N/A';
        }

        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
            return value.toString();
        }

        return numValue.toFixed(this.config.numericDifferencePrecision);
    }

    /**
     * Format percentage change as whole number
     */
    formatPercentageChange(value) {
        if (value === null || value === undefined || value === '' || isNaN(value)) {
            return 'N/A';
        }

        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
            return value.toString();
        }

        return Math.round(numValue).toString();
    }

    /**
     * Format currency value to 2 decimal places
     */
    formatCurrency(value, symbol = '') {
        if (value === null || value === undefined || value === '' || isNaN(value)) {
            return 'N/A';
        }

        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
            return value.toString();
        }

        const formatted = numValue.toFixed(this.config.currencyPrecision);
        return symbol ? `${symbol}${formatted}` : formatted;
    }

    /**
     * Calculate and format numeric difference
     */
    calculateAndFormatDifference(previousValue, currentValue) {
        const prev = parseFloat(previousValue);
        const curr = parseFloat(currentValue);

        if (isNaN(prev) || isNaN(curr)) {
            return 'N/A';
        }

        const difference = curr - prev;
        return this.formatNumericDifference(difference);
    }

    /**
     * Calculate and format percentage change
     */
    calculateAndFormatPercentageChange(previousValue, currentValue) {
        const prev = parseFloat(previousValue);
        const curr = parseFloat(currentValue);

        if (isNaN(prev) || isNaN(curr) || prev === 0) {
            return 'N/A';
        }

        const percentageChange = ((curr - prev) / prev) * 100;
        return this.formatPercentageChange(percentageChange);
    }

    /**
     * Format change description with proper numeric formatting
     */
    formatChangeDescription(change) {
        let description = '';
        
        if (change.employee_name) {
            description += `${change.employee_name}: `;
        }
        
        if (change.item_label || change.item_name) {
            description += `${change.item_label || change.item_name} `;
        }
        
        if (change.change_type) {
            description += `${change.change_type.toLowerCase()} `;
        }
        
        if (change.previous_value !== undefined && change.current_value !== undefined) {
            description += `from ${change.previous_value} to ${change.current_value}`;
            
            // Add formatted difference if numeric
            const formattedDiff = this.calculateAndFormatDifference(change.previous_value, change.current_value);
            if (formattedDiff !== 'N/A') {
                const diff = parseFloat(formattedDiff);
                if (diff > 0) {
                    description += ` (increase: ${formattedDiff})`;
                } else if (diff < 0) {
                    description += ` (decrease: ${Math.abs(diff).toFixed(2)})`;
                }
            }
        }
        
        return description;
    }

    /**
     * Apply formatting to all numeric elements in a container
     */
    applyFormattingToContainer(container) {
        if (!container) return;

        // Format numeric differences
        const numericDiffElements = container.querySelectorAll('.numeric-difference, [data-type="numeric-difference"]');
        numericDiffElements.forEach(element => {
            const value = element.textContent || element.getAttribute('data-value');
            element.textContent = this.formatNumericDifference(value);
        });

        // Format percentage changes
        const percentageElements = container.querySelectorAll('.percentage-change, [data-type="percentage-change"]');
        percentageElements.forEach(element => {
            const value = element.textContent || element.getAttribute('data-value');
            const formatted = this.formatPercentageChange(value);
            element.textContent = formatted !== 'N/A' ? `${formatted}%` : formatted;
        });

        // Format currency values
        const currencyElements = container.querySelectorAll('.currency-value, [data-type="currency"]');
        currencyElements.forEach(element => {
            const value = element.textContent || element.getAttribute('data-value');
            const symbol = element.getAttribute('data-currency-symbol') || '';
            element.textContent = this.formatCurrency(value, symbol);
        });

        console.log('[FORMATTING] Applied universal formatting to container');
    }

    /**
     * Update existing formatting functions to use universal standards
     */
    overrideExistingFormatters() {
        // Override global formatting functions if they exist
        if (window.formatNumericDifference) {
            window.formatNumericDifference = this.formatNumericDifference.bind(this);
        }
        
        if (window.formatPercentageChange) {
            window.formatPercentageChange = this.formatPercentageChange.bind(this);
        }
        
        if (window.calculateNumericDifference) {
            window.calculateNumericDifference = this.calculateAndFormatDifference.bind(this);
        }
        
        if (window.calculatePercentageChange) {
            window.calculatePercentageChange = this.calculateAndFormatPercentageChange.bind(this);
        }

        console.log('[FORMATTING] Overrode existing formatting functions with universal standards');
    }
}

// Create global instance
window.universalFormatter = new UniversalFormattingUtility();

// Auto-apply formatting when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Override existing formatters
    window.universalFormatter.overrideExistingFormatters();
    
    // Apply formatting to entire document
    window.universalFormatter.applyFormattingToContainer(document.body);
    
    // Set up mutation observer to format new content
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    window.universalFormatter.applyFormattingToContainer(node);
                }
            });
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

console.log('[FORMATTING] Universal Formatting Utility loaded');
