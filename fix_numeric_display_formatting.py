#!/usr/bin/env python3
"""
Fix Numeric Display Formatting
Ensure numeric differences are displayed to 2 decimal places and percentages as whole numbers
"""

import os
import re

def fix_javascript_numeric_formatting():
    """Fix JavaScript files to ensure consistent numeric formatting"""
    
    print("[FIX] FIXING JAVASCRIPT NUMERIC FORMATTING")
    print("=" * 50)
    
    js_files = [
        'renderer.js',
        'ui/interactive_pre_reporting.js',
        'main.js'
    ]
    
    fixes_applied = 0
    
    for js_file in js_files:
        if not os.path.exists(js_file):
            print(f"[SKIP] File not found: {js_file}")
            continue
        
        print(f"\nProcessing {js_file}:")
        
        try:
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Fix 1: Ensure Math.abs(difference) is formatted to 2 decimal places
            pattern1 = r'Math\.abs\(difference\)(?!\.\w)'
            replacement1 = 'Math.abs(difference).toFixed(2)'
            if re.search(pattern1, content):
                content = re.sub(pattern1, replacement1, content)
                print("   [FIXED] Math.abs(difference) formatting")
                fixes_applied += 1
            
            # Fix 2: Ensure difference calculations are formatted to 2 decimal places
            pattern2 = r'difference = curr - prev;'
            replacement2 = 'difference = parseFloat((curr - prev).toFixed(2));'
            if re.search(pattern2, content):
                content = re.sub(pattern2, replacement2, content)
                print("   [FIXED] Difference calculation formatting")
                fixes_applied += 1
            
            # Fix 3: Fix percentage calculations to be whole numbers
            pattern3 = r'const percentageChange = \(\(curr - prev\) / prev\) \* 100;'
            replacement3 = 'const percentageChange = Math.round(((curr - prev) / prev) * 100);'
            if re.search(pattern3, content):
                content = re.sub(pattern3, replacement3, content)
                print("   [FIXED] Percentage calculation formatting")
                fixes_applied += 1
            
            # Fix 4: Ensure toFixed(2) is used for numeric differences
            pattern4 = r'parseFloat\(value\)(?!\.\w)'
            replacement4 = 'parseFloat(value).toFixed(2)'
            # Only apply to specific contexts to avoid breaking other code
            if 'formatNumericDifference' in content or 'calculateDifference' in content:
                content = re.sub(pattern4, replacement4, content)
                print("   [FIXED] parseFloat formatting in numeric contexts")
                fixes_applied += 1
            
            # Write back if changes were made
            if content != original_content:
                with open(js_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"   [OK] Applied fixes to {js_file}")
            else:
                print(f"   [INFO] No changes needed in {js_file}")
                
        except Exception as e:
            print(f"   [FAIL] Error processing {js_file}: {e}")
    
    print(f"\n[RESULT] Applied {fixes_applied} total fixes to JavaScript files")
    return fixes_applied > 0

def create_universal_formatting_utility():
    """Create a universal formatting utility for consistent number display"""
    
    print("\n[FIX] CREATING UNIVERSAL FORMATTING UTILITY")
    print("=" * 50)
    
    utility_js = """
// Universal Formatting Utility
// Provides consistent number formatting throughout the application

class UniversalFormattingUtility {
    constructor() {
        // Configuration for number formatting
        this.config = {
            numericDifferencePrecision: 2,
            percentagePrecision: 0,
            currencyPrecision: 2,
            defaultPrecision: 2
        };
    }

    /**
     * Format numeric difference to 2 decimal places
     */
    formatNumericDifference(value) {
        if (value === null || value === undefined || value === '' || isNaN(value)) {
            return 'N/A';
        }

        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
            return value.toString();
        }

        return numValue.toFixed(this.config.numericDifferencePrecision);
    }

    /**
     * Format percentage change as whole number
     */
    formatPercentageChange(value) {
        if (value === null || value === undefined || value === '' || isNaN(value)) {
            return 'N/A';
        }

        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
            return value.toString();
        }

        return Math.round(numValue).toString();
    }

    /**
     * Format currency value to 2 decimal places
     */
    formatCurrency(value, symbol = '') {
        if (value === null || value === undefined || value === '' || isNaN(value)) {
            return 'N/A';
        }

        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
            return value.toString();
        }

        const formatted = numValue.toFixed(this.config.currencyPrecision);
        return symbol ? `${symbol}${formatted}` : formatted;
    }

    /**
     * Calculate and format numeric difference
     */
    calculateAndFormatDifference(previousValue, currentValue) {
        const prev = parseFloat(previousValue);
        const curr = parseFloat(currentValue);

        if (isNaN(prev) || isNaN(curr)) {
            return 'N/A';
        }

        const difference = curr - prev;
        return this.formatNumericDifference(difference);
    }

    /**
     * Calculate and format percentage change
     */
    calculateAndFormatPercentageChange(previousValue, currentValue) {
        const prev = parseFloat(previousValue);
        const curr = parseFloat(currentValue);

        if (isNaN(prev) || isNaN(curr) || prev === 0) {
            return 'N/A';
        }

        const percentageChange = ((curr - prev) / prev) * 100;
        return this.formatPercentageChange(percentageChange);
    }

    /**
     * Format change description with proper numeric formatting
     */
    formatChangeDescription(change) {
        let description = '';
        
        if (change.employee_name) {
            description += `${change.employee_name}: `;
        }
        
        if (change.item_label || change.item_name) {
            description += `${change.item_label || change.item_name} `;
        }
        
        if (change.change_type) {
            description += `${change.change_type.toLowerCase()} `;
        }
        
        if (change.previous_value !== undefined && change.current_value !== undefined) {
            description += `from ${change.previous_value} to ${change.current_value}`;
            
            // Add formatted difference if numeric
            const formattedDiff = this.calculateAndFormatDifference(change.previous_value, change.current_value);
            if (formattedDiff !== 'N/A') {
                const diff = parseFloat(formattedDiff);
                if (diff > 0) {
                    description += ` (increase: ${formattedDiff})`;
                } else if (diff < 0) {
                    description += ` (decrease: ${Math.abs(diff).toFixed(2)})`;
                }
            }
        }
        
        return description;
    }

    /**
     * Apply formatting to all numeric elements in a container
     */
    applyFormattingToContainer(container) {
        if (!container) return;

        // Format numeric differences
        const numericDiffElements = container.querySelectorAll('.numeric-difference, [data-type="numeric-difference"]');
        numericDiffElements.forEach(element => {
            const value = element.textContent || element.getAttribute('data-value');
            element.textContent = this.formatNumericDifference(value);
        });

        // Format percentage changes
        const percentageElements = container.querySelectorAll('.percentage-change, [data-type="percentage-change"]');
        percentageElements.forEach(element => {
            const value = element.textContent || element.getAttribute('data-value');
            const formatted = this.formatPercentageChange(value);
            element.textContent = formatted !== 'N/A' ? `${formatted}%` : formatted;
        });

        // Format currency values
        const currencyElements = container.querySelectorAll('.currency-value, [data-type="currency"]');
        currencyElements.forEach(element => {
            const value = element.textContent || element.getAttribute('data-value');
            const symbol = element.getAttribute('data-currency-symbol') || '';
            element.textContent = this.formatCurrency(value, symbol);
        });

        console.log('[FORMATTING] Applied universal formatting to container');
    }

    /**
     * Update existing formatting functions to use universal standards
     */
    overrideExistingFormatters() {
        // Override global formatting functions if they exist
        if (window.formatNumericDifference) {
            window.formatNumericDifference = this.formatNumericDifference.bind(this);
        }
        
        if (window.formatPercentageChange) {
            window.formatPercentageChange = this.formatPercentageChange.bind(this);
        }
        
        if (window.calculateNumericDifference) {
            window.calculateNumericDifference = this.calculateAndFormatDifference.bind(this);
        }
        
        if (window.calculatePercentageChange) {
            window.calculatePercentageChange = this.calculateAndFormatPercentageChange.bind(this);
        }

        console.log('[FORMATTING] Overrode existing formatting functions with universal standards');
    }
}

// Create global instance
window.universalFormatter = new UniversalFormattingUtility();

// Auto-apply formatting when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Override existing formatters
    window.universalFormatter.overrideExistingFormatters();
    
    // Apply formatting to entire document
    window.universalFormatter.applyFormattingToContainer(document.body);
    
    // Set up mutation observer to format new content
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    window.universalFormatter.applyFormattingToContainer(node);
                }
            });
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

console.log('[FORMATTING] Universal Formatting Utility loaded');
"""
    
    try:
        with open('ui/universal_formatting_utility.js', 'w', encoding='utf-8') as f:
            f.write(utility_js)
        
        print("[OK] Universal formatting utility created: ui/universal_formatting_utility.js")
        return True
        
    except Exception as e:
        print(f"[FAIL] Error creating universal formatting utility: {e}")
        return False

def update_database_numeric_calculations():
    """Update database to ensure numeric calculations are properly formatted"""

    print("\n[FIX] UPDATING DATABASE NUMERIC CALCULATIONS")
    print("=" * 50)

    try:
        import sqlite3

        db_path = 'payroll_audit.db'
        if not os.path.exists(db_path):
            print("[FAIL] Database not found")
            return False

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Add numeric_difference and percentage_change columns if they don't exist
        try:
            cursor.execute("ALTER TABLE comparison_results ADD COLUMN numeric_difference REAL")
            print("   [ADDED] numeric_difference column")
        except sqlite3.OperationalError:
            print("   [INFO] numeric_difference column already exists")

        try:
            cursor.execute("ALTER TABLE comparison_results ADD COLUMN percentage_change REAL")
            print("   [ADDED] percentage_change column")
        except sqlite3.OperationalError:
            print("   [INFO] percentage_change column already exists")

        # Calculate and update numeric differences
        cursor.execute("""
            UPDATE comparison_results
            SET numeric_difference = ROUND(
                CAST(current_value AS REAL) - CAST(previous_value AS REAL), 2
            )
            WHERE current_value IS NOT NULL
            AND previous_value IS NOT NULL
            AND current_value != ''
            AND previous_value != ''
            AND CAST(current_value AS REAL) IS NOT NULL
            AND CAST(previous_value AS REAL) IS NOT NULL
        """)

        updated_numeric = cursor.rowcount
        print(f"   [CALCULATED] {updated_numeric} numeric differences")

        # Calculate and update percentage changes
        cursor.execute("""
            UPDATE comparison_results
            SET percentage_change = ROUND(
                ((CAST(current_value AS REAL) - CAST(previous_value AS REAL)) / CAST(previous_value AS REAL)) * 100, 0
            )
            WHERE current_value IS NOT NULL
            AND previous_value IS NOT NULL
            AND current_value != ''
            AND previous_value != ''
            AND CAST(current_value AS REAL) IS NOT NULL
            AND CAST(previous_value AS REAL) IS NOT NULL
            AND CAST(previous_value AS REAL) != 0
        """)

        updated_percentage = cursor.rowcount
        print(f"   [CALCULATED] {updated_percentage} percentage changes")

        conn.commit()
        conn.close()

        print(f"[OK] Database numeric formatting updated")
        return True

    except Exception as e:
        print(f"[FAIL] Error updating database: {e}")
        return False

def verify_numeric_formatting():
    """Verify that numeric formatting is working correctly"""
    
    print("\n[CHECK] VERIFYING NUMERIC FORMATTING")
    print("=" * 50)
    
    try:
        import sqlite3
        
        db_path = 'payroll_audit.db'
        if not os.path.exists(db_path):
            print("[FAIL] Database not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check sample numeric differences
        cursor.execute("""
            SELECT employee_id, item_label, current_value, previous_value, numeric_difference, percentage_change
            FROM comparison_results
            WHERE numeric_difference IS NOT NULL
            LIMIT 5
        """)

        samples = cursor.fetchall()

        if samples:
            print("   Sample numeric formatting:")
            for emp_id, item, curr_val, prev_val, numeric_diff, percentage in samples:
                print(f"     {emp_id} - {item}: {prev_val} -> {curr_val}, diff={numeric_diff}, pct={percentage}%")
        else:
            print("   [INFO] No numeric differences found in database")
        
        # Check JavaScript files for formatting functions
        js_files = ['ui/universal_formatting_utility.js', 'ui/interactive_pre_reporting.js']
        
        for js_file in js_files:
            if os.path.exists(js_file):
                print(f"   [OK] {js_file} exists")
            else:
                print(f"   [MISSING] {js_file}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error verifying formatting: {e}")
        return False

def main():
    """Main function to run all numeric formatting fixes"""
    
    print("[FIX] COMPREHENSIVE NUMERIC DISPLAY FORMATTING FIX")
    print("=" * 70)
    
    success = True
    
    # Step 1: Fix JavaScript numeric formatting
    if not fix_javascript_numeric_formatting():
        print("[WARN] JavaScript formatting fixes had issues")
    
    # Step 2: Create universal formatting utility
    if not create_universal_formatting_utility():
        success = False
    
    # Step 3: Update database numeric calculations
    if not update_database_numeric_calculations():
        print("[WARN] Database formatting updates had issues")
    
    # Step 4: Verify formatting
    if not verify_numeric_formatting():
        print("[WARN] Formatting verification had issues")
    
    if success:
        print("\n[SUCCESS] All numeric formatting fixes completed!")
        print("The system should now:")
        print("  - Display numeric differences to 2 decimal places")
        print("  - Display percentage changes as whole numbers")
        print("  - Use consistent formatting throughout the interface")
        print("  - Have a universal formatting utility for consistency")
    else:
        print("\n[WARN] Some numeric formatting fixes had issues")
    
    return success

if __name__ == "__main__":
    main()
