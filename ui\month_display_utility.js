
// Month Display Utility
// Provides consistent month name display throughout the application

class MonthDisplayUtility {
    constructor() {
        this.monthNames = {
            'JAN': 'January', 'JANUARY': 'January',
            'FEB': 'February', 'FEBRUARY': 'February', 
            'MAR': 'March', 'MARCH': 'March',
            'APR': 'April', 'APRIL': 'April',
            'MAY': 'May',
            'JUN': 'June', 'JUNE': 'June',
            'JUL': 'July', 'JULY': 'July',
            'AUG': 'August', 'AUGUST': 'August',
            'SEP': 'September', 'SEPTEMBER': 'September',
            'OCT': 'October', 'OCTOBER': 'October',
            'NOV': 'November', 'NOVEMBER': 'November',
            'DEC': 'December', 'DECEMBER': 'December'
        };
    }

    /**
     * Convert month abbreviation or name to full month name
     */
    getFullMonthName(month) {
        if (!month) return 'Unknown Month';
        
        const upperMonth = month.toString().toUpperCase();
        return this.monthNames[upperMonth] || month;
    }

    /**
     * Format period display with actual month names
     */
    formatPeriodDisplay(sessionInfo) {
        if (!sessionInfo) {
            return 'Period Comparison';
        }

        const previousMonth = this.getFullMonthName(sessionInfo.previousMonth || sessionInfo.previous_month);
        const currentMonth = this.getFullMonthName(sessionInfo.currentMonth || sessionInfo.current_month);
        const previousYear = sessionInfo.previousYear || sessionInfo.previous_year || '';
        const currentYear = sessionInfo.currentYear || sessionInfo.current_year || '';

        return `${previousMonth} ${previousYear} → ${currentMonth} ${currentYear}`;
    }

    /**
     * Update UI elements with actual month names
     */
    updateMonthLabelsInUI(sessionInfo) {
        if (!sessionInfo) return;

        const currentMonth = this.getFullMonthName(sessionInfo.currentMonth || sessionInfo.current_month);
        const previousMonth = this.getFullMonthName(sessionInfo.previousMonth || sessionInfo.previous_month);

        // Update current month labels
        const currentMonthLabels = document.querySelectorAll('#current-month-label, .current-month-text');
        currentMonthLabels.forEach(label => {
            label.textContent = currentMonth;
        });

        // Update previous month labels
        const previousMonthLabels = document.querySelectorAll('#previous-month-label, .previous-month-text');
        previousMonthLabels.forEach(label => {
            label.textContent = previousMonth;
        });

        // Update period comparison displays
        const periodDisplays = document.querySelectorAll('.compared-periods, .period-display');
        periodDisplays.forEach(display => {
            display.textContent = this.formatPeriodDisplay(sessionInfo);
        });

        console.log(`[MONTH-DISPLAY] Updated UI with: ${previousMonth} → ${currentMonth}`);
    }

    /**
     * Replace generic period text with actual month names in any string
     */
    replaceGenericPeriodText(text, sessionInfo) {
        if (!text || !sessionInfo) return text;

        const currentMonth = this.getFullMonthName(sessionInfo.currentMonth || sessionInfo.current_month);
        const previousMonth = this.getFullMonthName(sessionInfo.previousMonth || sessionInfo.previous_month);
        const currentYear = sessionInfo.currentYear || sessionInfo.current_year || '';
        const previousYear = sessionInfo.previousYear || sessionInfo.previous_year || '';

        return text
            .replace(/Current Period/g, `${currentMonth} ${currentYear}`.trim())
            .replace(/Previous Period/g, `${previousMonth} ${previousYear}`.trim())
            .replace(/Current Month/g, currentMonth)
            .replace(/Previous Month/g, previousMonth);
    }
}

// Create global instance
window.monthDisplayUtility = new MonthDisplayUtility();

// Auto-update month labels when session info is available
document.addEventListener('DOMContentLoaded', () => {
    // Listen for session info updates
    document.addEventListener('sessionInfoUpdated', (event) => {
        if (event.detail && event.detail.sessionInfo) {
            window.monthDisplayUtility.updateMonthLabelsInUI(event.detail.sessionInfo);
        }
    });
});

console.log('[MONTH-DISPLAY] Month Display Utility loaded');
