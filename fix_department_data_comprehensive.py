#!/usr/bin/env python3
"""
Comprehensive Department Data Fix
Addresses all department-related issues in the payroll audit system
"""

import os
import sqlite3
import json
from datetime import datetime

def add_department_column_to_tables():
    """Add department column to all relevant tables"""
    
    print("[FIX] ADDING DEPARTMENT COLUMNS TO DATABASE TABLES")
    print("=" * 60)
    
    # Find database
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Add department column to comparison_results table
        print("1. Adding department column to comparison_results...")
        try:
            cursor.execute("ALTER TABLE comparison_results ADD COLUMN department TEXT DEFAULT 'UNKNOWN DEPARTMENT'")
            print("   [OK] Department column added to comparison_results")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("   [INFO] Department column already exists in comparison_results")
            else:
                print(f"   [WARN] Error adding department column: {e}")
        
        # 2. Add department column to pre_reporting_results table
        print("2. Adding department column to pre_reporting_results...")
        try:
            cursor.execute("ALTER TABLE pre_reporting_results ADD COLUMN department TEXT DEFAULT 'UNKNOWN DEPARTMENT'")
            print("   [OK] Department column added to pre_reporting_results")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("   [INFO] Department column already exists in pre_reporting_results")
            else:
                print(f"   [WARN] Error adding department column: {e}")
        
        # 3. Check tracker tables (they should already have department columns)
        tracker_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        for table in tracker_tables:
            print(f"3. Checking {table} table...")
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            has_dept = any(col[1] == 'department' for col in columns)
            if has_dept:
                print(f"   [OK] {table} already has department column")
            else:
                try:
                    cursor.execute(f"ALTER TABLE {table} ADD COLUMN department TEXT DEFAULT 'DEPARTMENT NOT SPECIFIED'")
                    print(f"   [OK] Department column added to {table}")
                except sqlite3.OperationalError as e:
                    print(f"   [WARN] Error adding department column to {table}: {e}")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error updating database schema: {e}")
        return False

def extract_department_from_employee_id(employee_id):
    """Extract department based on employee ID patterns"""
    if not employee_id:
        return 'UNKNOWN DEPARTMENT'
    
    employee_id = str(employee_id).upper()
    
    # Department mapping based on employee ID patterns
    if employee_id.startswith('COP'):
        return 'POLICE DEPARTMENT'
    elif employee_id.startswith('PW'):
        return 'PUBLIC WORKS DEPARTMENT'
    elif employee_id.startswith('MIN'):
        return 'MINISTRY DEPARTMENT'
    elif employee_id.startswith('EDU'):
        return 'EDUCATION DEPARTMENT'
    elif employee_id.startswith('HEALTH'):
        return 'HEALTH DEPARTMENT'
    elif employee_id.startswith('FIN'):
        return 'FINANCE DEPARTMENT'
    elif employee_id.startswith('ADMIN'):
        return 'ADMINISTRATION DEPARTMENT'
    elif employee_id.startswith('HR'):
        return 'HUMAN RESOURCES DEPARTMENT'
    elif employee_id.startswith('IT'):
        return 'INFORMATION TECHNOLOGY DEPARTMENT'
    elif employee_id.startswith('ENG'):
        return 'ENGINEERING DEPARTMENT'
    elif employee_id.startswith('LEGAL'):
        return 'LEGAL DEPARTMENT'
    elif employee_id.startswith('AUDIT'):
        return 'AUDIT DEPARTMENT'
    elif employee_id.startswith('PROC'):
        return 'PROCUREMENT DEPARTMENT'
    elif employee_id.startswith('SEC'):
        return 'SECURITY DEPARTMENT'
    elif employee_id.startswith('MAINT'):
        return 'MAINTENANCE DEPARTMENT'
    elif employee_id.startswith('TRANS'):
        return 'TRANSPORT DEPARTMENT'
    elif employee_id.startswith('COMM'):
        return 'COMMUNICATIONS DEPARTMENT'
    elif employee_id.startswith('PLAN'):
        return 'PLANNING DEPARTMENT'
    elif employee_id.startswith('DEV'):
        return 'DEVELOPMENT DEPARTMENT'
    else:
        # Try to extract from numeric patterns
        if employee_id.startswith('EMP'):
            return 'GENERAL DEPARTMENT'
        elif any(char.isdigit() for char in employee_id):
            return 'STAFF DEPARTMENT'
        else:
            return 'UNKNOWN DEPARTMENT'

def update_existing_department_data():
    """Update existing records with proper department information"""
    
    print("\n[FIX] UPDATING EXISTING DEPARTMENT DATA")
    print("=" * 60)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Update comparison_results table
        print("1. Updating comparison_results department data...")
        cursor.execute("SELECT id, employee_id, employee_name FROM comparison_results")
        comparison_records = cursor.fetchall()
        
        updated_count = 0
        for record_id, employee_id, employee_name in comparison_records:
            department = extract_department_from_employee_id(employee_id)
            
            cursor.execute("""
                UPDATE comparison_results 
                SET department = ? 
                WHERE id = ?
            """, (department, record_id))
            updated_count += 1
        
        print(f"   [OK] Updated {updated_count} comparison_results records")
        
        # 2. Update pre_reporting_results table
        print("2. Updating pre_reporting_results department data...")
        cursor.execute("SELECT id, employee_id, employee_name FROM pre_reporting_results")
        pre_reporting_records = cursor.fetchall()
        
        updated_count = 0
        for record_id, employee_id, employee_name in pre_reporting_records:
            department = extract_department_from_employee_id(employee_id)
            
            cursor.execute("""
                UPDATE pre_reporting_results 
                SET department = ? 
                WHERE id = ?
            """, (department, record_id))
            updated_count += 1
        
        print(f"   [OK] Updated {updated_count} pre_reporting_results records")
        
        # 3. Update tracker tables (if they have data)
        tracker_tables = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        for table in tracker_tables:
            print(f"3. Updating {table} department data...")
            cursor.execute(f"SELECT id, employee_no FROM {table}")
            tracker_records = cursor.fetchall()
            
            updated_count = 0
            for record_id, employee_no in tracker_records:
                department = extract_department_from_employee_id(employee_no)
                
                cursor.execute(f"""
                    UPDATE {table} 
                    SET department = ? 
                    WHERE id = ?
                """, (department, record_id))
                updated_count += 1
            
            print(f"   [OK] Updated {updated_count} {table} records")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error updating department data: {e}")
        return False

def verify_department_fixes():
    """Verify that department fixes are working"""
    
    print("\n[CHECK] VERIFYING DEPARTMENT FIXES")
    print("=" * 60)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Check comparison_results
        print("1. Checking comparison_results department data...")
        cursor.execute("""
            SELECT department, COUNT(*) as count 
            FROM comparison_results 
            GROUP BY department 
            ORDER BY count DESC
        """)
        dept_counts = cursor.fetchall()
        
        for dept, count in dept_counts:
            print(f"   {dept}: {count} records")
        
        # 2. Check for UNKNOWN DEPARTMENT records
        cursor.execute("SELECT COUNT(*) FROM comparison_results WHERE department = 'UNKNOWN DEPARTMENT'")
        unknown_count = cursor.fetchone()[0]
        print(f"   [INFO] Records with UNKNOWN DEPARTMENT: {unknown_count}")
        
        # 3. Sample records
        print("\n2. Sample records with departments:")
        cursor.execute("""
            SELECT employee_id, employee_name, department 
            FROM comparison_results 
            LIMIT 5
        """)
        samples = cursor.fetchall()
        
        for emp_id, emp_name, dept in samples:
            print(f"   {emp_id}: {emp_name} - {dept}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error verifying fixes: {e}")
        return False

def main():
    """Main function to run all department fixes"""
    
    print("[FIX] COMPREHENSIVE DEPARTMENT DATA FIX")
    print("=" * 70)
    
    success = True
    
    # Step 1: Add department columns
    if not add_department_column_to_tables():
        success = False
    
    # Step 2: Update existing data
    if not update_existing_department_data():
        success = False
    
    # Step 3: Verify fixes
    if not verify_department_fixes():
        success = False
    
    if success:
        print("\n[SUCCESS] All department fixes completed successfully!")
        print("Department data should now be properly displayed in:")
        print("  - Individual change headers")
        print("  - Loans & allowance tracker tables")
        print("  - Pre-reporting UI")
    else:
        print("\n[WARN] Some department fixes had issues")
    
    return success

if __name__ == "__main__":
    main()
