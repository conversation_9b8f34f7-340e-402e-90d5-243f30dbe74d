#!/usr/bin/env python3
"""
Comprehensive Section Classification Fix
Fix all section misclassifications in the database
"""

import os
import sqlite3
from datetime import datetime

def get_correct_section_mapping():
    """Get comprehensive section mapping for all items"""
    return {
        # PERSONAL DETAILS
        'EMPLOYEE NO': 'PERSONAL DETAILS',
        '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> NAME': 'PERSONAL DETAILS',
        '<PERSON>MPLOYEE NUMBER': 'PERSONAL DETAILS',
        'NAME': 'PERSONAL DETAILS',
        'DEPARTMENT': 'PERSONAL DETAILS',
        'SECTION': 'PERSONAL DETAILS',
        'JOB TITLE': 'PERSONAL DETAILS',
        'POSITION': 'PERSONAL DETAILS',
        'GRADE': 'PERSONAL DETAILS',
        'LEVEL': 'PERSONAL DETAILS',
        'SSF NUMBER': 'PERSONAL DETAILS',
        'SSNIT NUMBER': 'PERSONAL DETAILS',
        'GHANA CARD ID': 'PERSONAL DETAILS',
        'NATIONAL ID': 'PERSONAL DETAILS',
        'DATE OF BIRTH': 'PERSONAL DETAILS',
        'GENDER': 'PERSONAL DETAILS',
        'MARITAL STATUS': 'PERSONAL DETAILS',
        
        # EARNINGS
        'BASIC SALARY': 'EARNINGS',
        'BASIC PAY': 'EARNINGS',
        'SALARY': 'EARNINGS',
        'OVERTIME': 'EARNINGS',
        'OVERTIME PAY': 'EARNINGS',
        'BONUS': 'EARNINGS',
        'COMMISSION': 'EARNINGS',
        'ALLOWANCE': 'EARNINGS',
        'HOUSING ALLOWANCE': 'EARNINGS',
        'TRANSPORT ALLOWANCE': 'EARNINGS',
        'MEAL ALLOWANCE': 'EARNINGS',
        'MEDICAL ALLOWANCE': 'EARNINGS',
        'EDUCATION ALLOWANCE': 'EARNINGS',
        'RESPONSIBILITY ALLOWANCE': 'EARNINGS',
        'ACTING ALLOWANCE': 'EARNINGS',
        'SHIFT ALLOWANCE': 'EARNINGS',
        'HAZARD ALLOWANCE': 'EARNINGS',
        'UNIFORM ALLOWANCE': 'EARNINGS',
        'FUEL ALLOWANCE': 'EARNINGS',
        'COMMUNICATION ALLOWANCE': 'EARNINGS',
        'ENTERTAINMENT ALLOWANCE': 'EARNINGS',
        'PROFESSIONAL ALLOWANCE': 'EARNINGS',
        'TECHNICAL ALLOWANCE': 'EARNINGS',
        'SUPERVISORY ALLOWANCE': 'EARNINGS',
        'SPECIAL ALLOWANCE': 'EARNINGS',
        'ARREARS': 'EARNINGS',
        'BACK PAY': 'EARNINGS',
        'GROSS PAY': 'EARNINGS',
        'GROSS SALARY': 'EARNINGS',
        'TOTAL EARNINGS': 'EARNINGS',
        
        # DEDUCTIONS
        'INCOME TAX': 'DEDUCTIONS',
        'TAX': 'DEDUCTIONS',
        'PAYE': 'DEDUCTIONS',
        'WITHHOLDING TAX': 'DEDUCTIONS',
        'SSNIT': 'DEDUCTIONS',
        'SSF': 'DEDUCTIONS',
        'SOCIAL SECURITY': 'DEDUCTIONS',
        'PENSION': 'DEDUCTIONS',
        'PROVIDENT FUND': 'DEDUCTIONS',
        'INSURANCE': 'DEDUCTIONS',
        'HEALTH INSURANCE': 'DEDUCTIONS',
        'LIFE INSURANCE': 'DEDUCTIONS',
        'GROUP INSURANCE': 'DEDUCTIONS',
        'UNION DUES': 'DEDUCTIONS',
        'WELFARE': 'DEDUCTIONS',
        'WELFARE FUND': 'DEDUCTIONS',
        'COOPERATIVE': 'DEDUCTIONS',
        'SAVINGS': 'DEDUCTIONS',
        'MOTOR VEH. MAINTENAN': 'DEDUCTIONS',
        'MOTOR VEHICLE MAINTENANCE': 'DEDUCTIONS',
        'VEHICLE MAINTENANCE': 'DEDUCTIONS',
        'CAR MAINTENANCE': 'DEDUCTIONS',
        'MAINTENANCE': 'DEDUCTIONS',
        'REPAIR': 'DEDUCTIONS',
        'FUEL DEDUCTION': 'DEDUCTIONS',
        'PARKING': 'DEDUCTIONS',
        'UNIFORM DEDUCTION': 'DEDUCTIONS',
        'MEDICAL DEDUCTION': 'DEDUCTIONS',
        'ADVANCE': 'DEDUCTIONS',
        'SALARY ADVANCE': 'DEDUCTIONS',
        'OVERPAYMENT': 'DEDUCTIONS',
        'RECOVERY': 'DEDUCTIONS',
        'GARNISHMENT': 'DEDUCTIONS',
        'COURT ORDER': 'DEDUCTIONS',
        'ATTACHMENT': 'DEDUCTIONS',
        'FINE': 'DEDUCTIONS',
        'PENALTY': 'DEDUCTIONS',
        'DISCIPLINARY': 'DEDUCTIONS',
        'ABSENT DAYS': 'DEDUCTIONS',
        'LATE COMING': 'DEDUCTIONS',
        'TOTAL DEDUCTIONS': 'DEDUCTIONS',
        
        # LOANS
        'STAFF LOAN': 'LOANS',
        'PERSONAL LOAN': 'LOANS',
        'CAR LOAN': 'LOANS',
        'VEHICLE LOAN': 'LOANS',
        'HOUSING LOAN': 'LOANS',
        'MORTGAGE': 'LOANS',
        'EDUCATION LOAN': 'LOANS',
        'EMERGENCY LOAN': 'LOANS',
        'SALARY LOAN': 'LOANS',
        'ADVANCE LOAN': 'LOANS',
        'BANK LOAN': 'LOANS',
        'MICROFINANCE': 'LOANS',
        'CREDIT UNION': 'LOANS',
        'COOPERATIVE LOAN': 'LOANS',
        'SUSU': 'LOANS',
        'RENT ADVANCE': 'LOANS',
        'BALANCE B/F': 'LOANS',
        'LOAN BALANCE': 'LOANS',
        'OUTSTANDING': 'LOANS',
        'PRINCIPAL': 'LOANS',
        'INTEREST': 'LOANS',
        'LOAN REPAYMENT': 'LOANS',
        'MONTHLY DEDUCTION': 'LOANS',
        
        # EMPLOYERS CONTRIBUTION
        'EMPLOYER SSF': 'EMPLOYERS CONTRIBUTION',
        'EMPLOYER SSNIT': 'EMPLOYERS CONTRIBUTION',
        'EMPLOYER PENSION': 'EMPLOYERS CONTRIBUTION',
        'EMPLOYER INSURANCE': 'EMPLOYERS CONTRIBUTION',
        'EMPLOYER CONTRIBUTION': 'EMPLOYERS CONTRIBUTION',
        'COMPANY CONTRIBUTION': 'EMPLOYERS CONTRIBUTION',
        'ORGANIZATION CONTRIBUTION': 'EMPLOYERS CONTRIBUTION',
        'PROVIDENT FUND EMPLOYER': 'EMPLOYERS CONTRIBUTION',
        'PENSION EMPLOYER': 'EMPLOYERS CONTRIBUTION',
        'GRATUITY': 'EMPLOYERS CONTRIBUTION',
        'END OF SERVICE': 'EMPLOYERS CONTRIBUTION',
        'SEVERANCE': 'EMPLOYERS CONTRIBUTION',
        
        # EMPLOYEE BANK DETAILS
        'BANK ACCOUNT': 'EMPLOYEE BANK DETAILS',
        'ACCOUNT NUMBER': 'EMPLOYEE BANK DETAILS',
        'BANK NAME': 'EMPLOYEE BANK DETAILS',
        'BANK CODE': 'EMPLOYEE BANK DETAILS',
        'BRANCH': 'EMPLOYEE BANK DETAILS',
        'BRANCH CODE': 'EMPLOYEE BANK DETAILS',
        'SORT CODE': 'EMPLOYEE BANK DETAILS',
        'ROUTING NUMBER': 'EMPLOYEE BANK DETAILS',
        'SWIFT CODE': 'EMPLOYEE BANK DETAILS',
        'IBAN': 'EMPLOYEE BANK DETAILS',
        'PAYMENT METHOD': 'EMPLOYEE BANK DETAILS',
        'BANK DETAILS': 'EMPLOYEE BANK DETAILS',
        'ACCOUNT DETAILS': 'EMPLOYEE BANK DETAILS',
        'ACCOUNT TYPE': 'EMPLOYEE BANK DETAILS',
        'ACCOUNT HOLDER': 'EMPLOYEE BANK DETAILS',
        'BENEFICIARY': 'EMPLOYEE BANK DETAILS',
        'NET PAY': 'EMPLOYEE BANK DETAILS',
        'NET SALARY': 'EMPLOYEE BANK DETAILS',
        'TAKE HOME': 'EMPLOYEE BANK DETAILS',
        'AMOUNT PAID': 'EMPLOYEE BANK DETAILS',
        'TOTAL PAYMENT': 'EMPLOYEE BANK DETAILS'
    }

def fix_section_misclassifications():
    """Fix all section misclassifications in the database"""
    
    print("[FIX] COMPREHENSIVE SECTION CLASSIFICATION FIX")
    print("=" * 70)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get section mapping
        section_mapping = get_correct_section_mapping()
        
        print("1. CHECKING CURRENT MISCLASSIFICATIONS:")
        cursor.execute("SELECT DISTINCT section, item_label FROM comparison_results ORDER BY section, item_label")
        current_items = cursor.fetchall()
        
        misclassified_items = []
        for section, item_label in current_items:
            correct_section = section_mapping.get(item_label.upper())
            if correct_section and correct_section != section:
                misclassified_items.append((section, item_label, correct_section))
                print(f"   [MISCLASSIFIED] {item_label}: {section} -> {correct_section}")
        
        if not misclassified_items:
            print("   [OK] No misclassifications found")
            return True
        
        print(f"\n2. FIXING {len(misclassified_items)} MISCLASSIFICATIONS:")
        
        total_fixes = 0
        for old_section, item_label, correct_section in misclassified_items:
            # Update comparison_results
            cursor.execute("""
                UPDATE comparison_results 
                SET section = ? 
                WHERE item_label = ? AND section = ?
            """, (correct_section, item_label, old_section))
            
            comp_fixes = cursor.rowcount
            
            # Update pre_reporting_results if it exists
            cursor.execute("""
                UPDATE pre_reporting_results 
                SET section = ? 
                WHERE item_label = ? AND section = ?
            """, (correct_section, item_label, old_section))
            
            pre_fixes = cursor.rowcount
            
            print(f"   [FIXED] {item_label}: {old_section} -> {correct_section} ({comp_fixes + pre_fixes} records)")
            total_fixes += comp_fixes + pre_fixes
        
        conn.commit()
        
        print(f"\n3. VERIFICATION:")
        cursor.execute("SELECT DISTINCT section, item_label FROM comparison_results ORDER BY section, item_label")
        updated_items = cursor.fetchall()
        
        print("   Current section assignments:")
        current_section = None
        for section, item_label in updated_items:
            if section != current_section:
                print(f"\n   {section}:")
                current_section = section
            print(f"     - {item_label}")
        
        conn.close()
        
        print(f"\n[SUCCESS] Fixed {total_fixes} section misclassifications!")
        return True
        
    except Exception as e:
        print(f"[FAIL] Error fixing section misclassifications: {e}")
        return False

def main():
    """Main function"""
    
    success = fix_section_misclassifications()
    
    if success:
        print("\n[SUCCESS] Section classification fix completed!")
        print("All items should now be in their correct sections for clean reporting.")
    else:
        print("\n[WARN] Section classification fix had issues")
    
    return success

if __name__ == "__main__":
    main()
