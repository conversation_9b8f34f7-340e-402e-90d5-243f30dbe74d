
def feed_to_tracker_with_department(session_id, employee_id, employee_name, department, item_label, item_value, numeric_value, tracker_type):
    """Feed data to tracker_results with proper department information"""
    
    # Ensure department is not empty
    if not department or department.strip() in ['', 'None', 'UNKNOWN']:
        # Extract from employee ID pattern
        if employee_id.startswith('COP'):
            department = 'POLICE DEPARTMENT'
        elif employee_id.startswith('PW'):
            department = 'PUBLIC WORKS DEPARTMENT'
        elif employee_id.startswith('MIN'):
            department = 'MINISTRY DEPARTMENT'
        elif employee_id.startswith('FIN'):
            department = 'FINANCE DEPARTMENT'
        elif employee_id.startswith('HR'):
            department = 'HUMAN RESOURCES DEPARTMENT'
        elif employee_id.startswith('ENG'):
            department = 'ENGINEERING DEPARTMENT'
        elif employee_id.startswith('ADMIN'):
            department = 'ADMINISTRATION DEPARTMENT'
        elif employee_id.startswith('IT'):
            department = 'INFORMATION TECHNOLOGY DEPARTMENT'
        else:
            department = 'DEPARTMENT NOT SPECIFIED'
    
    # Insert into tracker_results with department
    cursor.execute("""
        INSERT INTO tracker_results 
        (session_id, employee_id, employee_name, department, item_label, item_value, numeric_value, tracker_type)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    """, (session_id, employee_id, employee_name, department, item_label, item_value, numeric_value, tracker_type))
    
    print(f"[TRACKER] Fed {employee_id}: {employee_name} ({department}) - {item_label}")

def extract_department_from_comparison_result(comparison_result):
    """Extract department from comparison result data"""
    
    # Try to get department from comparison result
    if isinstance(comparison_result, dict):
        dept = comparison_result.get('department')
        if dept and dept.strip() not in ['', 'None', 'UNKNOWN', 'DEPARTMENT NOT SPECIFIED']:
            return dept.strip()
        
        # Fallback to employee ID pattern
        employee_id = comparison_result.get('employee_id', '')
        return extract_department_from_employee_id(employee_id)
    
    return 'DEPARTMENT NOT SPECIFIED'

def extract_department_from_employee_id(employee_id):
    """Extract department based on employee ID patterns"""
    if not employee_id:
        return 'DEPARTMENT NOT SPECIFIED'
    
    employee_id = str(employee_id).upper()
    
    if employee_id.startswith('COP'):
        return 'POLICE DEPARTMENT'
    elif employee_id.startswith('PW'):
        return 'PUBLIC WORKS DEPARTMENT'
    elif employee_id.startswith('MIN'):
        return 'MINISTRY DEPARTMENT'
    elif employee_id.startswith('FIN'):
        return 'FINANCE DEPARTMENT'
    elif employee_id.startswith('HR'):
        return 'HUMAN RESOURCES DEPARTMENT'
    elif employee_id.startswith('ENG'):
        return 'ENGINEERING DEPARTMENT'
    elif employee_id.startswith('ADMIN'):
        return 'ADMINISTRATION DEPARTMENT'
    elif employee_id.startswith('IT'):
        return 'INFORMATION TECHNOLOGY DEPARTMENT'
    else:
        return 'DEPARTMENT NOT SPECIFIED'
