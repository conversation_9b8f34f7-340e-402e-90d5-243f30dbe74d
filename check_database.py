#!/usr/bin/env python3
"""
Check Database Structure
Simple script to examine the database structure and content
"""

import os
import sqlite3

def check_database():
    """Check database structure and content"""
    
    print("[CHECK] EXAMINING DATABASE STRUCTURE")
    print("=" * 50)
    
    # Find database
    possible_db_names = ['payroll_auditor.db', 'payroll_audit.db', 'templar_payroll_auditor.db']
    db_path = None
    
    for db_name in possible_db_names:
        if os.path.exists(db_name):
            db_path = db_name
            print(f"[INFO] Found database: {db_name}")
            break
    
    if not db_path:
        print("[FAIL] No database found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"\n[INFO] Found {len(tables)} tables:")
        for table in tables:
            table_name = table[0]
            print(f"   - {table_name}")
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"     Rows: {count}")
            
            # Get column info
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"     Columns: {[col[1] for col in columns]}")
            
            print()
        
        # Check for specific tables we need
        required_tables = ['audit_sessions', 'comparison_results', 'employees', 'reports']
        
        print("[CHECK] Required tables status:")
        for req_table in required_tables:
            exists = any(t[0] == req_table for t in tables)
            status = "[OK]" if exists else "[MISSING]"
            print(f"   {status} {req_table}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error checking database: {e}")
        return False

if __name__ == "__main__":
    check_database()
