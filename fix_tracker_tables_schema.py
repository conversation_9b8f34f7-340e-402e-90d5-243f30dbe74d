#!/usr/bin/env python3
"""
Fix Tracker Tables Schema
Fix database schema issues and test department functionality properly
"""

import os
import sqlite3
from datetime import datetime

def check_and_fix_tracker_table_schemas():
    """Check and fix tracker table schemas"""
    
    print("[FIX] CHECKING AND FIXING TRACKER TABLE SCHEMAS")
    print("=" * 60)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check existing table structures
        tables_to_check = ['in_house_loans', 'external_loans', 'motor_vehicle_maintenance']
        
        for table_name in tables_to_check:
            print(f"\n{table_name.upper()}:")
            
            # Get current table structure
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            if columns:
                print("   Current columns:")
                column_names = []
                for col in columns:
                    column_names.append(col[1])
                    print(f"     - {col[1]} ({col[2]})")
                
                # Add missing columns
                missing_columns = []
                
                if 'department' not in column_names:
                    missing_columns.append(('department', 'TEXT'))
                if 'remarks' not in column_names:
                    missing_columns.append(('remarks', 'TEXT'))
                if 'source_session' not in column_names:
                    missing_columns.append(('source_session', 'TEXT'))
                
                # Add missing columns
                for col_name, col_type in missing_columns:
                    try:
                        cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {col_name} {col_type}")
                        print(f"   [ADDED] {col_name} column")
                    except sqlite3.OperationalError as e:
                        print(f"   [INFO] {col_name} column: {e}")
            else:
                print("   [WARN] Table does not exist, creating...")
                
                if table_name == 'in_house_loans':
                    cursor.execute("""
                        CREATE TABLE in_house_loans (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            employee_no TEXT NOT NULL,
                            employee_name TEXT NOT NULL,
                            department TEXT DEFAULT 'DEPARTMENT NOT SPECIFIED',
                            loan_type TEXT NOT NULL,
                            loan_amount REAL NOT NULL,
                            period_month TEXT,
                            period_year INTEGER,
                            period_acquired TEXT,
                            source_session TEXT,
                            remarks TEXT,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    print("   [CREATED] in_house_loans table")
                
                elif table_name == 'external_loans':
                    cursor.execute("""
                        CREATE TABLE external_loans (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            employee_no TEXT NOT NULL,
                            employee_name TEXT NOT NULL,
                            department TEXT DEFAULT 'DEPARTMENT NOT SPECIFIED',
                            loan_type TEXT NOT NULL,
                            loan_amount REAL NOT NULL,
                            period_month TEXT,
                            period_year INTEGER,
                            period_acquired TEXT,
                            source_session TEXT,
                            remarks TEXT,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    print("   [CREATED] external_loans table")
                
                elif table_name == 'motor_vehicle_maintenance':
                    cursor.execute("""
                        CREATE TABLE motor_vehicle_maintenance (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            employee_no TEXT NOT NULL,
                            employee_name TEXT NOT NULL,
                            department TEXT DEFAULT 'DEPARTMENT NOT SPECIFIED',
                            allowance_type TEXT NOT NULL,
                            allowance_amount REAL NOT NULL,
                            payable_amount REAL,
                            maintenance_amount REAL,
                            period_month TEXT,
                            period_year INTEGER,
                            period_acquired TEXT,
                            source_session TEXT,
                            remarks TEXT,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    print("   [CREATED] motor_vehicle_maintenance table")
        
        conn.commit()
        conn.close()
        
        print("\n[SUCCESS] Tracker table schemas fixed")
        return True
        
    except Exception as e:
        print(f"[FAIL] Error fixing tracker table schemas: {e}")
        return False

def insert_real_department_test_data():
    """Insert test data with real departments"""
    
    print("\n[TEST] INSERTING REAL DEPARTMENT TEST DATA")
    print("=" * 60)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute("SELECT session_id FROM sessions ORDER BY created_at DESC LIMIT 1")
        session_result = cursor.fetchone()
        current_session = session_result[0] if session_result else 'test_session'
        
        print(f"[INFO] Using session: {current_session}")
        
        # Clear existing test data
        cursor.execute("DELETE FROM in_house_loans WHERE remarks = 'Real department test data'")
        cursor.execute("DELETE FROM external_loans WHERE remarks = 'Real department test data'")
        cursor.execute("DELETE FROM motor_vehicle_maintenance WHERE remarks = 'Real department test data'")
        
        # 1. In-House Loans with Real Departments
        print("\n1. IN-HOUSE LOANS:")
        in_house_loans = [
            ('COP001', 'OFFICER JOHN MENSAH', 'POLICE DEPARTMENT', 'STAFF LOAN', 15000.00),
            ('PW002', 'ENGINEER MARY ASANTE', 'PUBLIC WORKS DEPARTMENT', 'EMERGENCY LOAN', 8000.00),
            ('FIN003', 'DIRECTOR KWAME OSEI', 'FINANCE DEPARTMENT', 'HOUSING LOAN', 50000.00),
            ('HR004', 'MANAGER AKOSUA OWUSU', 'HUMAN RESOURCES DEPARTMENT', 'PERSONAL LOAN', 12000.00)
        ]
        
        for emp_no, emp_name, dept, loan_type, amount in in_house_loans:
            cursor.execute("""
                INSERT INTO in_house_loans 
                (employee_no, employee_name, department, loan_type, loan_amount, 
                 period_month, period_year, period_acquired, source_session, remarks)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (emp_no, emp_name, dept, loan_type, amount, 'June', 2025, 'June 2025', current_session, 'Real department test data'))
            
            print(f"   [INSERTED] {emp_no}: {emp_name} - {dept} ({loan_type}: {amount})")
        
        # 2. External Loans with Real Departments
        print("\n2. EXTERNAL LOANS:")
        external_loans = [
            ('ENG005', 'CHIEF ENGINEER KOFI ADJEI', 'ENGINEERING DEPARTMENT', 'BANK LOAN', 25000.00),
            ('ADMIN006', 'ADMIN OFFICER AMA SERWAA', 'ADMINISTRATION DEPARTMENT', 'MICROFINANCE', 5000.00)
        ]
        
        for emp_no, emp_name, dept, loan_type, amount in external_loans:
            cursor.execute("""
                INSERT INTO external_loans 
                (employee_no, employee_name, department, loan_type, loan_amount, 
                 period_month, period_year, period_acquired, source_session, remarks)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (emp_no, emp_name, dept, loan_type, amount, 'June', 2025, 'June 2025', current_session, 'Real department test data'))
            
            print(f"   [INSERTED] {emp_no}: {emp_name} - {dept} ({loan_type}: {amount})")
        
        # 3. Motor Vehicle Maintenance with Real Departments
        print("\n3. MOTOR VEHICLE MAINTENANCE:")
        vehicle_maintenance = [
            ('COP007', 'SERGEANT AKOSUA BOATENG', 'POLICE DEPARTMENT', 'MOTOR VEH. MAINTENAN', 1200.00),
            ('PW008', 'SUPERVISOR KWAKU ASANTE', 'PUBLIC WORKS DEPARTMENT', 'VEHICLE MAINTENANCE', 1500.00),
            ('IT009', 'IT SPECIALIST ABENA OSEI', 'INFORMATION TECHNOLOGY DEPARTMENT', 'MOTOR VEHICLE MAINTENANCE', 1000.00)
        ]
        
        for emp_no, emp_name, dept, allowance_type, amount in vehicle_maintenance:
            cursor.execute("""
                INSERT INTO motor_vehicle_maintenance 
                (employee_no, employee_name, department, allowance_type, allowance_amount, 
                 payable_amount, maintenance_amount, period_month, period_year, 
                 period_acquired, source_session, remarks)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (emp_no, emp_name, dept, allowance_type, amount, amount, amount, 'June', 2025, 'June 2025', current_session, 'Real department test data'))
            
            print(f"   [INSERTED] {emp_no}: {emp_name} - {dept} ({allowance_type}: {amount})")
        
        conn.commit()
        conn.close()
        
        print(f"\n[SUCCESS] Inserted real department test data")
        return True
        
    except Exception as e:
        print(f"[FAIL] Error inserting test data: {e}")
        return False

def verify_department_display():
    """Verify department display in tracker tables"""
    
    print("\n[VERIFY] DEPARTMENT DISPLAY VERIFICATION")
    print("=" * 60)
    
    db_path = 'payroll_audit.db'
    if not os.path.exists(db_path):
        print("[FAIL] Database not found")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check each tracker table
        tables = [
            ('in_house_loans', 'In-House Loans', 'loan_type', 'loan_amount'),
            ('external_loans', 'External Loans', 'loan_type', 'loan_amount'),
            ('motor_vehicle_maintenance', 'Motor Vehicle Maintenance', 'allowance_type', 'allowance_amount')
        ]
        
        for table_name, display_name, type_col, amount_col in tables:
            print(f"\n{display_name.upper()}:")
            
            # Get department distribution
            cursor.execute(f"""
                SELECT department, COUNT(*) as count 
                FROM {table_name} 
                GROUP BY department 
                ORDER BY count DESC
            """)
            dept_counts = cursor.fetchall()
            
            if dept_counts:
                print("   Department distribution:")
                for dept, count in dept_counts:
                    print(f"     - {dept}: {count} records")
                
                # Show records with real departments
                cursor.execute(f"""
                    SELECT employee_no, employee_name, department, {type_col}, {amount_col}
                    FROM {table_name} 
                    WHERE department NOT IN ('GENERAL DEPARTMENT', 'DEPARTMENT NOT SPECIFIED', 'UNKNOWN DEPARTMENT')
                    ORDER BY id DESC
                    LIMIT 5
                """)
                real_dept_records = cursor.fetchall()
                
                if real_dept_records:
                    print("   Records with real departments:")
                    for emp_no, emp_name, dept, loan_type, amount in real_dept_records:
                        print(f"     - {emp_no}: {emp_name} ({dept}) - {loan_type}: {amount}")
                else:
                    print("   [WARN] No records with real departments found")
                
                # Count records with real vs generic departments
                cursor.execute(f"""
                    SELECT 
                        SUM(CASE WHEN department NOT IN ('GENERAL DEPARTMENT', 'DEPARTMENT NOT SPECIFIED', 'UNKNOWN DEPARTMENT') THEN 1 ELSE 0 END) as real_dept,
                        SUM(CASE WHEN department IN ('GENERAL DEPARTMENT', 'DEPARTMENT NOT SPECIFIED', 'UNKNOWN DEPARTMENT') THEN 1 ELSE 0 END) as generic_dept
                    FROM {table_name}
                """)
                dept_stats = cursor.fetchone()
                real_count, generic_count = dept_stats
                
                print(f"   Statistics: {real_count} real departments, {generic_count} generic departments")
                
            else:
                print("   [INFO] No records found in table")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] Error verifying department display: {e}")
        return False

def main():
    """Main function to test tracker department functionality"""
    
    print("[TEST] COMPREHENSIVE TRACKER DEPARTMENT FUNCTIONALITY TEST")
    print("=" * 70)
    
    success = True
    
    # Step 1: Fix table schemas
    if not check_and_fix_tracker_table_schemas():
        success = False
    
    # Step 2: Insert real department test data
    if not insert_real_department_test_data():
        success = False
    
    # Step 3: Verify department display
    if not verify_department_display():
        success = False
    
    if success:
        print("\n[SUCCESS] Tracker department functionality test completed!")
        print("Results:")
        print("  ✅ Tracker tables have proper schema with department columns")
        print("  ✅ Real department data inserted successfully")
        print("  ✅ Department display verified in all tracker tables")
        print("  ✅ No more 'department not specified' for test records")
    else:
        print("\n[WARN] Some tracker department tests had issues")
    
    return success

if __name__ == "__main__":
    main()
