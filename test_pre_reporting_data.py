#!/usr/bin/env python3
"""
Quick test to diagnose pre-reporting data loading issues
"""

import sys
import os
import sqlite3
import json

# Add the current directory to Python path
sys.path.append(os.path.dirname(__file__))

def test_database_connection():
    """Test basic database connectivity"""
    print("1. 🔍 TESTING DATABASE CONNECTION:")
    
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    
    if not os.path.exists(db_path):
        print(f"   ❌ Database file not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test basic query
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"   ✅ Database connected successfully")
        print(f"   📊 Found {len(tables)} tables")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Database connection failed: {e}")
        return False

def test_pre_reporting_tables():
    """Test pre-reporting related tables"""
    print("\n2. 📋 TESTING PRE-REPORTING TABLES:")
    
    db_path = os.path.join('data', 'templar_payroll_auditor.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check pre_reporting_results table
        cursor.execute("SELECT COUNT(*) FROM pre_reporting_results")
        pre_reporting_count = cursor.fetchone()[0]
        print(f"   📊 pre_reporting_results: {pre_reporting_count} records")
        
        # Check audit_sessions table
        cursor.execute("SELECT COUNT(*) FROM audit_sessions")
        sessions_count = cursor.fetchone()[0]
        print(f"   📊 audit_sessions: {sessions_count} records")
        
        if sessions_count > 0:
            cursor.execute("SELECT session_id, created_at FROM audit_sessions ORDER BY created_at DESC LIMIT 1")
            latest_session = cursor.fetchone()
            print(f"   📊 Latest session: {latest_session[0]} ({latest_session[1]})")
            
            # Check if this session has pre-reporting data
            cursor.execute("SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?", (latest_session[0],))
            session_pre_reporting = cursor.fetchone()[0]
            print(f"   📊 Pre-reporting data for latest session: {session_pre_reporting} records")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Table check failed: {e}")
        return False

def test_phased_process_manager():
    """Test the PhasedProcessManager directly"""
    print("\n3. 🔧 TESTING PHASED PROCESS MANAGER:")
    
    try:
        from core.phased_process_manager import PhasedProcessManager
        
        manager = PhasedProcessManager()
        print("   ✅ PhasedProcessManager created successfully")
        
        # Test get_pre_reporting_data method
        result = manager.get_pre_reporting_data()
        
        print(f"   📊 Result type: {type(result)}")
        print(f"   📊 Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        if isinstance(result, dict):
            print(f"   📊 Success: {result.get('success', 'Not specified')}")
            print(f"   📊 Error: {result.get('error', 'None')}")
            print(f"   📊 Data count: {len(result.get('data', [])) if result.get('data') else 0}")
            print(f"   📊 Total changes: {result.get('total_changes', 'Not specified')}")
            print(f"   📊 Session ID: {result.get('session_id', 'Not specified')}")
        
        return result
        
    except Exception as e:
        print(f"   ❌ PhasedProcessManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    print("🧪 PRE-REPORTING DATA DIAGNOSTIC TEST")
    print("=" * 50)
    
    # Test 1: Database connection
    db_ok = test_database_connection()
    
    # Test 2: Pre-reporting tables
    if db_ok:
        tables_ok = test_pre_reporting_tables()
    else:
        tables_ok = False
    
    # Test 3: PhasedProcessManager
    if tables_ok:
        result = test_phased_process_manager()
        
        if result and result.get('success'):
            print("\n🎉 SUCCESS: Pre-reporting data is available!")
            print("   The issue is likely in the UI loading or rendering logic.")
        else:
            print("\n❌ FAILURE: Pre-reporting data is not available.")
            print("   The issue is in the backend data preparation.")
    
    print("\n" + "=" * 50)
    print("🔍 DIAGNOSTIC COMPLETE")

if __name__ == "__main__":
    main()
